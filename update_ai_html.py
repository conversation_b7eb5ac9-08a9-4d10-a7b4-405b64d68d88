#!/usr/bin/env python3
import re

def main():
    # 读取新的模型数据
    with open('new_model_data.js', 'r') as f:
        new_model_data = f.read()
    
    # 读取 ai.html
    with open('ai.html', 'r') as f:
        content = f.read()
    
    # 找到 modelData 的开始和结束位置
    start_pattern = r'this\.modelData = {'
    end_pattern = r'};'
    
    # 找到开始位置
    start_match = re.search(start_pattern, content)
    if not start_match:
        print("找不到 modelData 开始位置")
        return
    
    start_pos = start_match.start()
    
    # 从开始位置之后找到对应的结束位置
    remaining_content = content[start_pos:]
    brace_count = 0
    end_pos = -1
    
    for i, char in enumerate(remaining_content):
        if char == '{':
            brace_count += 1
        elif char == '}':
            brace_count -= 1
            if brace_count == 0:
                end_pos = start_pos + i + 1
                break
    
    if end_pos == -1:
        print("找不到 modelData 结束位置")
        return
    
    # 替换内容
    new_content = content[:start_pos] + new_model_data.strip() + content[end_pos:]
    
    # 写回文件
    with open('ai.html', 'w') as f:
        f.write(new_content)
    
    print("成功更新 ai.html 中的模型数据")

if __name__ == '__main__':
    main()
