#!/usr/bin/env python3
import re

def main():
    # 读取 in.txt 中的所有模型
    with open('in.txt', 'r') as f:
        in_models = set()
        for line in f:
            line = line.strip()
            if line and line not in ['fake', 'costly', 'abuse']:
                in_models.add(line)

    print(f'in.txt 中的模型数量: {len(in_models)}')

    # 读取 ai.html 中的模型
    with open('ai.html', 'r') as f:
        content = f.read()

    # 提取所有 value 字段
    html_models = set(re.findall(r"{ value: '([^']+)', text:", content))
    print(f'ai.html 中的模型数量: {len(html_models)}')

    # 检查差异
    missing_in_html = in_models - html_models
    extra_in_html = html_models - in_models

    print(f'\nin.txt 中有但 ai.html 中缺失的模型数量: {len(missing_in_html)}')
    if missing_in_html:
        print('缺失的模型:')
        for i, model in enumerate(sorted(missing_in_html)):
            print(f'  {model}')
            if i >= 50:  # 限制输出数量
                print(f'  ... 还有 {len(missing_in_html) - 51} 个')
                break

    print(f'\nai.html 中有但 in.txt 中没有的模型数量: {len(extra_in_html)}')
    if extra_in_html:
        print('多余的模型:')
        for i, model in enumerate(sorted(extra_in_html)):
            print(f'  {model}')
            if i >= 50:  # 限制输出数量
                print(f'  ... 还有 {len(extra_in_html) - 51} 个')
                break

if __name__ == '__main__':
    main()
