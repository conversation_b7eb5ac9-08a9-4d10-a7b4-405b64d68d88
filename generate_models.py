#!/usr/bin/env python3
import re

def categorize_model(model):
    """根据模型名称判断所属厂商"""
    if model.startswith('gpt-') or model.startswith('o1') or model.startswith('o3') or model.startswith('o4') or model.startswith('openrouter:openai/'):
        return 'openai'
    elif model.startswith('claude-') or model.startswith('openrouter:anthropic/'):
        return 'anthropic'
    elif model.startswith('gemini-') or model.startswith('google/') or model.startswith('openrouter:google/'):
        return 'google'
    elif model.startswith('meta-llama/') or model.startswith('openrouter:meta-llama/'):
        return 'meta'
    elif model.startswith('mistral') or model.startswith('ministral') or model.startswith('codestral') or model.startswith('devstral') or model.startswith('pixtral') or model.startswith('magistral') or model.startswith('open-mistral') or model.startswith('open-mixtral') or model.startswith('mistralai/') or model.startswith('openrouter:mistralai/'):
        return 'mistral'
    elif model.startswith('deepseek') or model.startswith('deepseek-ai/') or model.startswith('openrouter:deepseek/'):
        return 'deepseek'
    elif model.startswith('qwen') or model.startswith('Qwen/') or model.startswith('openrouter:qwen/'):
        return 'qwen'
    elif model.startswith('grok') or model.startswith('openrouter:x-ai/'):
        return 'xai'
    elif model.startswith('openrouter:microsoft/'):
        return 'microsoft'
    elif model.startswith('openrouter:nvidia/') or model.startswith('nvidia/'):
        return 'nvidia'
    elif model.startswith('openrouter:cohere/'):
        return 'cohere'
    elif model.startswith('openrouter:amazon/'):
        return 'amazon'
    else:
        return 'other'

def format_model_name(model):
    """格式化模型显示名称"""
    # 移除 openrouter: 前缀用于显示
    display_name = model.replace('openrouter:', '')
    
    # 将连字符和下划线替换为空格，并进行首字母大写
    display_name = display_name.replace('-', ' ').replace('_', ' ').replace('/', ' ')
    
    # 特殊处理一些常见的缩写
    display_name = display_name.replace('gpt', 'GPT')
    display_name = display_name.replace('claude', 'Claude')
    display_name = display_name.replace('gemini', 'Gemini')
    display_name = display_name.replace('llama', 'Llama')
    display_name = display_name.replace('qwen', 'Qwen')
    display_name = display_name.replace('mistral', 'Mistral')
    display_name = display_name.replace('deepseek', 'DeepSeek')
    display_name = display_name.replace('grok', 'Grok')
    
    # 如果是 OpenRouter 模型，添加标识
    if model.startswith('openrouter:'):
        display_name += ' (OpenRouter)'
    
    return display_name

def main():
    # 读取 in.txt 中的所有模型
    with open('in.txt', 'r') as f:
        models = []
        for line in f:
            line = line.strip()
            if line and line not in ['fake', 'costly', 'abuse']:
                models.append(line)

    # 按厂商分类
    categorized = {
        'openai': [],
        'anthropic': [],
        'google': [],
        'meta': [],
        'mistral': [],
        'deepseek': [],
        'qwen': [],
        'cohere': [],
        'amazon': [],
        'xai': [],
        'microsoft': [],
        'nvidia': [],
        'other': []
    }

    for model in models:
        category = categorize_model(model)
        display_name = format_model_name(model)
        categorized[category].append({
            'value': model,
            'text': display_name
        })

    # 生成 JavaScript 代码
    print("this.modelData = {")
    for category, models_list in categorized.items():
        if models_list:  # 只输出有模型的分类
            print(f"    {category}: [")
            for i, model_info in enumerate(models_list):
                comma = "," if i < len(models_list) - 1 else ""
                print(f"        {{ value: '{model_info['value']}', text: '{model_info['text']}' }}{comma}")
            print("    ],")
    print("};")

if __name__ == '__main__':
    main()
