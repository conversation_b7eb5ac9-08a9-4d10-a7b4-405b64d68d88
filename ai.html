<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 大模型对话框</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            font-family: 'Inter', sans-serif;
        }
        
        .chat-container {
            max-height: 70vh;
            overflow-y: auto;
        }
        
        .message {
            animation: fadeIn 0.3s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .typing-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #3b82f6;
            animation: typing 1.4s infinite ease-in-out;
            margin-right: 4px;
        }
        
        .typing-indicator:nth-child(1) { animation-delay: -0.32s; }
        .typing-indicator:nth-child(2) { animation-delay: -0.16s; }
        
        @keyframes typing {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }
        
        .file-upload-area {
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .file-upload-area:hover {
            border-color: #3b82f6;
            background-color: #f8fafc;
        }
        
        .file-upload-area.drag-over {
            border-color: #3b82f6;
            background-color: #eff6ff;
        }
        
        .file-preview {
            max-width: 200px;
            max-height: 200px;
            object-fit: cover;
            border-radius: 8px;
        }
        
        .model-select {
            max-height: 200px;
            overflow-y: auto;
        }
        
        .scrollbar-thin {
            scrollbar-width: thin;
            scrollbar-color: #cbd5e1 #f1f5f9;
        }
        
        .scrollbar-thin::-webkit-scrollbar {
            width: 6px;
        }
        
        .scrollbar-thin::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }
        
        .scrollbar-thin::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        
        .scrollbar-thin::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <!-- 标题 -->
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">
                    <i class="fas fa-robot text-blue-600 mr-2"></i>
                    AI 大模型对话框
                </h1>
                <p class="text-gray-600">支持多种大模型的智能对话助手</p>
            </div>
            
            <!-- 控制面板 -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- 模型选择 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-cog mr-1"></i>选择模型
                        </label>
                        <!-- 厂商选择 -->
                        <select id="providerSelect" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent mb-2">
                            <option value="">请选择厂商</option>
                            <option value="openai">OpenAI</option>
                            <option value="anthropic">Anthropic</option>
                            <option value="google">Google</option>
                            <option value="meta">Meta</option>
                            <option value="mistral">Mistral AI</option>
                            <option value="deepseek">DeepSeek</option>
                            <option value="qwen">Qwen</option>
                            <option value="cohere">Cohere</option>
                            <option value="amazon">Amazon</option>
                            <option value="xai">xAI</option>
                            <option value="microsoft">Microsoft</option>
                            <option value="nvidia">NVIDIA</option>
                            <option value="other">其他厂商</option>
                            <option value="custom">自定义模型</option>
                        </select>
                        <!-- 模型选择 -->
                        <select id="modelSelect" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent model-select scrollbar-thin mb-2" disabled>
                            <option value="">请先选择厂商</option>
                        </select>
                        <!-- 自定义模型输入 -->
                        <div id="customModelInput" class="hidden">
                            <input type="text" id="customModel" placeholder="输入自定义模型名称，如: openrouter:vendor/model-name" 
                                   class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                            <div class="text-xs text-gray-500 mt-1">
                                <i class="fas fa-info-circle mr-1"></i>
                                支持格式：model-name 或 openrouter:vendor/model-name
                            </div>
                        </div>
                    </div>


                        </select>
                    </div>
                    
                    <!-- 历史记录管理 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-history mr-1"></i>历史记录
                        </label>
                        <div class="flex space-x-2">
                            <button id="saveHistory" class="flex-1 bg-green-600 text-white p-2 rounded-lg hover:bg-green-700 transition-colors">
                                <i class="fas fa-save mr-1"></i>保存
                            </button>
                            <button id="loadHistory" class="flex-1 bg-blue-600 text-white p-2 rounded-lg hover:bg-blue-700 transition-colors">
                                <i class="fas fa-upload mr-1"></i>加载
                            </button>
                            <button id="clearHistory" class="flex-1 bg-red-600 text-white p-2 rounded-lg hover:bg-red-700 transition-colors">
                                <i class="fas fa-trash mr-1"></i>清空
                            </button>
                        </div>
                    </div>
                    
                    <!-- 统计信息 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-chart-bar mr-1"></i>对话统计
                        </label>
                        <div class="bg-gray-50 p-3 rounded-lg">
                            <div class="text-sm text-gray-600">
                                <div>消息数量: <span id="messageCount" class="font-medium text-blue-600">0</span></div>
                                <div>当前模型: <span id="currentModel" class="font-medium text-green-600">GPT-4o</span></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 对话区域 -->
            <div class="bg-white rounded-lg shadow-lg mb-6">
                <div class="p-6">
                    <div id="chatMessages" class="chat-container scrollbar-thin space-y-4">
                        <div class="text-center text-gray-500 py-8">
                            <i class="fas fa-comments text-4xl mb-4"></i>
                            <p>开始与AI对话吧！支持文本、图片、文档等多种输入方式。</p>
                        </div>
                    </div>
                </div>
                
                <!-- 输入区域 -->
                <div class="border-t p-6">
                    <!-- 文件上传区域 -->
                    <div id="fileUploadArea" class="file-upload-area mb-4 hidden">
                        <i class="fas fa-cloud-upload-alt text-2xl text-gray-400 mb-2"></i>
                        <p class="text-gray-600">拖拽文件到这里或点击上传</p>
                        <p class="text-sm text-gray-400">支持图片、文档、视频等多种格式</p>
                        <input type="file" id="fileInput" class="hidden" multiple accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt,.json,.csv">
                    </div>
                    
                    <!-- 已上传文件预览 -->
                    <div id="filePreview" class="mb-4 hidden">
                        <div class="flex flex-wrap gap-2" id="filePreviewList"></div>
                    </div>
                    
                    <!-- 消息输入 -->
                    <div class="flex space-x-4">
                        <button id="attachBtn" class="bg-gray-100 text-gray-600 p-3 rounded-lg hover:bg-gray-200 transition-colors">
                            <i class="fas fa-paperclip"></i>
                        </button>
                        <div class="flex-1 relative">
                            <textarea id="messageInput" 
                                placeholder="输入您的消息..." 
                                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                                rows="3"></textarea>
                        </div>
                        <button id="sendBtn" class="bg-blue-600 text-white p-3 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 隐藏的文件输入用于加载历史记录 -->
    <input type="file" id="historyFileInput" class="hidden" accept=".json">
    
    <script src="https://js.puter.com/v2/"></script>
    <script>
        class ChatInterface {
            constructor() {
                this.messages = [];
                this.currentFiles = [];
                this.isProcessing = false;
                
                this.initializeElements();
                this.setupEventListeners();
                this.setupModelData();
                this.loadFromLocalStorage();
            }
            
            setupModelData() {
                this.modelData = {
                    openai: [
                        { value: 'gpt-4o', text: 'GPT-4o' },
                        { value: 'gpt-4o-mini', text: 'GPT-4o Mini' },
                        { value: 'o1', text: 'O1' },
                        { value: 'o1-mini', text: 'O1 Mini' },
                        { value: 'o1-pro', text: 'O1 Pro' },
                        { value: 'o3', text: 'O3' },
                        { value: 'o3-mini', text: 'O3 Mini' },
                        { value: 'o4-mini', text: 'O4 Mini' },
                        { value: 'gpt-4.1', text: 'GPT-4.1' },
                        { value: 'gpt-4.1-mini', text: 'GPT-4.1 Mini' },
                        { value: 'gpt-4.1-nano', text: 'GPT-4.1 Nano' },
                        { value: 'gpt-4.5-preview', text: 'GPT-4.5 Preview' },
                        { value: 'openrouter:openai/whisper-large-v3', text: 'Whisper Large V3 (OpenRouter)' },
                        { value: 'openrouter:openai/gpt-4', text: 'GPT-4 (OpenRouter)' },
                        { value: 'openrouter:openai/gpt-4o', text: 'GPT-4o (OpenRouter)' },
                        { value: 'openrouter:openai/gpt-4o-mini', text: 'GPT-4o Mini (OpenRouter)' },
                        { value: 'openrouter:openai/gpt-4o-2024-05-13', text: 'GPT-4o 2024-05-13 (OpenRouter)' },
                        { value: 'openrouter:openai/gpt-4o-2024-08-06', text: 'GPT-4o 2024-08-06 (OpenRouter)' },
                        { value: 'openrouter:openai/gpt-4o-2024-11-20', text: 'GPT-4o 2024-11-20 (OpenRouter)' },
                        { value: 'openrouter:openai/gpt-4o-mini-2024-07-18', text: 'GPT-4o Mini 2024-07-18 (OpenRouter)' },
                        { value: 'openrouter:openai/gpt-4o-mini-search-preview', text: 'GPT-4o Mini Search Preview (OpenRouter)' },
                        { value: 'openrouter:openai/gpt-4o-search-preview', text: 'GPT-4o Search Preview (OpenRouter)' },
                        { value: 'openrouter:openai/gpt-4o:extended', text: 'GPT-4o Extended (OpenRouter)' },
                        { value: 'openrouter:openai/gpt-4.1', text: 'GPT-4.1 (OpenRouter)' },
                        { value: 'openrouter:openai/gpt-4.1-mini', text: 'GPT-4.1 Mini (OpenRouter)' },
                        { value: 'openrouter:openai/gpt-4.1-nano', text: 'GPT-4.1 Nano (OpenRouter)' },
                        { value: 'openrouter:openai/gpt-4.5-preview', text: 'GPT-4.5 Preview (OpenRouter)' },
                        { value: 'openrouter:openai/gpt-4-turbo', text: 'GPT-4 Turbo (OpenRouter)' },
                        { value: 'openrouter:openai/gpt-4-turbo-preview', text: 'GPT-4 Turbo Preview (OpenRouter)' },
                        { value: 'openrouter:openai/gpt-4-1106-preview', text: 'GPT-4 1106 Preview (OpenRouter)' },
                        { value: 'openrouter:openai/gpt-4-0314', text: 'GPT-4 0314 (OpenRouter)' },
                        { value: 'openrouter:openai/gpt-3.5-turbo-0613', text: 'GPT-3.5 Turbo 0613 (OpenRouter)' },
                        { value: 'openrouter:openai/gpt-3.5-turbo-16k', text: 'GPT-3.5 Turbo 16K (OpenRouter)' },
                        { value: 'openrouter:openai/gpt-3.5-turbo-instruct', text: 'GPT-3.5 Turbo Instruct (OpenRouter)' },
                        { value: 'openrouter:openai/o1', text: 'O1 (OpenRouter)' },
                        { value: 'openrouter:openai/o1-mini', text: 'O1 Mini (OpenRouter)' },
                        { value: 'openrouter:openai/o1-mini-2024-09-12', text: 'O1 Mini 2024-09-12 (OpenRouter)' },
                        { value: 'openrouter:openai/o1-preview', text: 'O1 Preview (OpenRouter)' },
                        { value: 'openrouter:openai/o1-preview-2024-09-12', text: 'O1 Preview 2024-09-12 (OpenRouter)' },
                        { value: 'openrouter:openai/o1-pro', text: 'O1 Pro (OpenRouter)' },
                        { value: 'openrouter:openai/o3', text: 'O3 (OpenRouter)' },
                        { value: 'openrouter:openai/o3-mini', text: 'O3 Mini (OpenRouter)' },
                        { value: 'openrouter:openai/o3-mini-high', text: 'O3 Mini High (OpenRouter)' },
                        { value: 'openrouter:openai/o3-pro', text: 'O3 Pro (OpenRouter)' },
                        { value: 'openrouter:openai/o4-mini', text: 'O4 Mini (OpenRouter)' },
                        { value: 'openrouter:openai/o4-mini-high', text: 'O4 Mini High (OpenRouter)' },
                        { value: 'openrouter:openai/chatgpt-4o-latest', text: 'ChatGPT-4o Latest (OpenRouter)' },
                        { value: 'openrouter:openai/codex-mini', text: 'Codex Mini (OpenRouter)' },
                        { value: 'openrouter:openai/gpt-3.5-turbo-instruct', text: 'GPT-3.5 Turbo Instruct (OpenRouter)' }
                    ],
                    anthropic: [
                        { value: 'claude-opus-4-20250514', text: 'Claude Opus 4 20250514' },
                        { value: 'claude-opus-4', text: 'Claude Opus 4' },
                        { value: 'claude-opus-4-latest', text: 'Claude Opus 4 Latest' },
                        { value: 'claude-sonnet-4-20250514', text: 'Claude Sonnet 4 20250514' },
                        { value: 'claude-sonnet-4', text: 'Claude Sonnet 4' },
                        { value: 'claude-sonnet-4-latest', text: 'Claude Sonnet 4 Latest' },
                        { value: 'claude-3-7-sonnet-20250219', text: 'Claude 3.7 Sonnet 20250219' },
                        { value: 'claude-3-7-sonnet-latest', text: 'Claude 3.7 Sonnet Latest' },
                        { value: 'claude-3-5-sonnet-20241022', text: 'Claude 3.5 Sonnet 20241022' },
                        { value: 'claude-3-5-sonnet-latest', text: 'Claude 3.5 Sonnet Latest' },
                        { value: 'claude-3-5-sonnet-20240620', text: 'Claude 3.5 Sonnet 20240620' },
                        { value: 'claude-3-haiku-20240307', text: 'Claude 3 Haiku 20240307' },
                        { value: 'openrouter:anthropic/claude-2', text: 'Claude 2 (OpenRouter)' },
                        { value: 'openrouter:anthropic/claude-2.0', text: 'Claude 2.0 (OpenRouter)' },
                        { value: 'openrouter:anthropic/claude-2.0:beta', text: 'Claude 2.0 Beta (OpenRouter)' },
                        { value: 'openrouter:anthropic/claude-2.1', text: 'Claude 2.1 (OpenRouter)' },
                        { value: 'openrouter:anthropic/claude-2.1:beta', text: 'Claude 2.1 Beta (OpenRouter)' },
                        { value: 'openrouter:anthropic/claude-2:beta', text: 'Claude 2 Beta (OpenRouter)' },
                        { value: 'openrouter:anthropic/claude-3-haiku', text: 'Claude 3 Haiku (OpenRouter)' },
                        { value: 'openrouter:anthropic/claude-3-haiku:beta', text: 'Claude 3 Haiku Beta (OpenRouter)' },
                        { value: 'openrouter:anthropic/claude-3-opus', text: 'Claude 3 Opus (OpenRouter)' },
                        { value: 'openrouter:anthropic/claude-3-opus:beta', text: 'Claude 3 Opus Beta (OpenRouter)' },
                        { value: 'openrouter:anthropic/claude-3-sonnet', text: 'Claude 3 Sonnet (OpenRouter)' },
                        { value: 'openrouter:anthropic/claude-3-sonnet:beta', text: 'Claude 3 Sonnet Beta (OpenRouter)' },
                        { value: 'openrouter:anthropic/claude-3.5-haiku', text: 'Claude 3.5 Haiku (OpenRouter)' },
                        { value: 'openrouter:anthropic/claude-3.5-haiku-20241022', text: 'Claude 3.5 Haiku 20241022 (OpenRouter)' },
                        { value: 'openrouter:anthropic/claude-3.5-haiku-20241022:beta', text: 'Claude 3.5 Haiku 20241022 Beta (OpenRouter)' },
                        { value: 'openrouter:anthropic/claude-3.5-haiku:beta', text: 'Claude 3.5 Haiku Beta (OpenRouter)' },
                        { value: 'openrouter:anthropic/claude-3.5-sonnet', text: 'Claude 3.5 Sonnet (OpenRouter)' },
                        { value: 'openrouter:anthropic/claude-3.5-sonnet-20240620', text: 'Claude 3.5 Sonnet 20240620 (OpenRouter)' },
                        { value: 'openrouter:anthropic/claude-3.5-sonnet-20240620:beta', text: 'Claude 3.5 Sonnet 20240620 Beta (OpenRouter)' },
                        { value: 'openrouter:anthropic/claude-3.5-sonnet:beta', text: 'Claude 3.5 Sonnet Beta (OpenRouter)' },
                        { value: 'openrouter:anthropic/claude-3.7-sonnet', text: 'Claude 3.7 Sonnet (OpenRouter)' },
                        { value: 'openrouter:anthropic/claude-3.7-sonnet:beta', text: 'Claude 3.7 Sonnet Beta (OpenRouter)' },
                        { value: 'openrouter:anthropic/claude-3.7-sonnet:thinking', text: 'Claude 3.7 Sonnet Thinking (OpenRouter)' },
                        { value: 'openrouter:anthropic/claude-opus-4', text: 'Claude Opus 4 (OpenRouter)' },
                        { value: 'openrouter:anthropic/claude-sonnet-4', text: 'Claude Sonnet 4 (OpenRouter)' },
                        { value: 'openrouter:anthropic/claude-instant-1.1', text: 'Claude Instant 1.1 (OpenRouter)' }
                    ],
                    google: [
                        { value: 'gemini-1.5-flash', text: 'Gemini 1.5 Flash' },
                        { value: 'gemini-2.0-flash', text: 'Gemini 2.0 Flash' },
                        { value: 'google/gemini-2.0-flash-001', text: 'Gemini 2.0 Flash 001' },
                        { value: 'google/gemini-2.0-flash-exp:free', text: 'Gemini 2.0 Flash Exp Free' },
                        { value: 'google/gemini-2.0-flash-lite-001', text: 'Gemini 2.0 Flash Lite 001' },
                        { value: 'google/gemini-2.5-flash', text: 'Gemini 2.5 Flash' },
                        { value: 'google/gemini-2.5-flash-lite-preview-06-17', text: 'Gemini 2.5 Flash Lite Preview 06-17' },
                        { value: 'google/gemini-2.5-flash-preview', text: 'Gemini 2.5 Flash Preview' },
                        { value: 'google/gemini-2.5-flash-preview-05-20', text: 'Gemini 2.5 Flash Preview 05-20' },
                        { value: 'google/gemini-2.5-flash-preview-05-20:thinking', text: 'Gemini 2.5 Flash Preview 05-20 Thinking' },
                        { value: 'google/gemini-2.5-flash-preview:thinking', text: 'Gemini 2.5 Flash Preview Thinking' },
                        { value: 'google/gemini-2.5-pro', text: 'Gemini 2.5 Pro' },
                        { value: 'google/gemini-2.5-pro-exp-03-25', text: 'Gemini 2.5 Pro Exp 03-25' },
                        { value: 'google/gemini-2.5-pro-preview', text: 'Gemini 2.5 Pro Preview' },
                        { value: 'google/gemini-2.5-pro-preview-05-06', text: 'Gemini 2.5 Pro Preview 05-06' },
                        { value: 'google/gemini-flash-1.5', text: 'Gemini Flash 1.5' },
                        { value: 'google/gemini-flash-1.5-8b', text: 'Gemini Flash 1.5 8B' },
                        { value: 'google/gemini-pro-1.5', text: 'Gemini Pro 1.5' },
                        { value: 'google/gemma-3-27b-it', text: 'Gemma 3 27B IT' },
                        { value: 'google/gemma-2-27b-it', text: 'Gemma 2 27B IT' },
                        { value: 'google/gemma-3n-E4B-it', text: 'Gemma 3n E4B IT' },
                        { value: 'openrouter:google/gemini-2.0-flash-001', text: 'Gemini 2.0 Flash 001 (OpenRouter)' },
                        { value: 'openrouter:google/gemini-2.0-flash-exp:free', text: 'Gemini 2.0 Flash Exp Free (OpenRouter)' },
                        { value: 'openrouter:google/gemini-2.0-flash-lite-001', text: 'Gemini 2.0 Flash Lite 001 (OpenRouter)' },
                        { value: 'openrouter:google/gemini-2.5-flash', text: 'Gemini 2.5 Flash (OpenRouter)' },
                        { value: 'openrouter:google/gemini-2.5-flash-lite-preview-06-17', text: 'Gemini 2.5 Flash Lite Preview 06-17 (OpenRouter)' },
                        { value: 'openrouter:google/gemini-2.5-flash-preview', text: 'Gemini 2.5 Flash Preview (OpenRouter)' },
                        { value: 'openrouter:google/gemini-2.5-flash-preview-05-20', text: 'Gemini 2.5 Flash Preview 05-20 (OpenRouter)' },
                        { value: 'openrouter:google/gemini-2.5-flash-preview-05-20:thinking', text: 'Gemini 2.5 Flash Preview 05-20 Thinking (OpenRouter)' },
                        { value: 'openrouter:google/gemini-2.5-flash-preview:thinking', text: 'Gemini 2.5 Flash Preview Thinking (OpenRouter)' },
                        { value: 'openrouter:google/gemini-2.5-pro', text: 'Gemini 2.5 Pro (OpenRouter)' },
                        { value: 'openrouter:google/gemini-2.5-pro-exp-03-25', text: 'Gemini 2.5 Pro Exp 03-25 (OpenRouter)' },
                        { value: 'openrouter:google/gemini-2.5-pro-preview', text: 'Gemini 2.5 Pro Preview (OpenRouter)' },
                        { value: 'openrouter:google/gemini-2.5-pro-preview-05-06', text: 'Gemini 2.5 Pro Preview 05-06 (OpenRouter)' },
                        { value: 'openrouter:google/gemini-flash-1.5', text: 'Gemini Flash 1.5 (OpenRouter)' },
                        { value: 'openrouter:google/gemini-flash-1.5-8b', text: 'Gemini Flash 1.5 8B (OpenRouter)' },
                        { value: 'openrouter:google/gemini-pro-1.5', text: 'Gemini Pro 1.5 (OpenRouter)' },
                        { value: 'openrouter:google/gemma-2-27b-it', text: 'Gemma 2 27B IT (OpenRouter)' },
                        { value: 'openrouter:google/gemma-2-9b-it', text: 'Gemma 2 9B IT (OpenRouter)' },
                        { value: 'openrouter:google/gemma-2-9b-it:free', text: 'Gemma 2 9B IT Free (OpenRouter)' },
                        { value: 'openrouter:google/gemma-3-12b-it', text: 'Gemma 3 12B IT (OpenRouter)' },
                        { value: 'openrouter:google/gemma-3-12b-it:free', text: 'Gemma 3 12B IT Free (OpenRouter)' },
                        { value: 'openrouter:google/gemma-3-27b-it', text: 'Gemma 3 27B IT (OpenRouter)' },
                        { value: 'openrouter:google/gemma-3-27b-it:free', text: 'Gemma 3 27B IT Free (OpenRouter)' },
                        { value: 'openrouter:google/gemma-3-4b-it', text: 'Gemma 3 4B IT (OpenRouter)' },
                        { value: 'openrouter:google/gemma-3-4b-it:free', text: 'Gemma 3 4B IT Free (OpenRouter)' },
                        { value: 'openrouter:google/gemma-3n-e2b-it:free', text: 'Gemma 3n E2B IT Free (OpenRouter)' },
                        { value: 'openrouter:google/gemma-3n-e4b-it', text: 'Gemma 3n E4B IT (OpenRouter)' },
                        { value: 'openrouter:google/gemma-3n-e4b-it:free', text: 'Gemma 3n E4B IT Free (OpenRouter)' }
                    ],
                    meta: [
                        { value: 'meta-llama/llama-3.1-405b-instruct', text: 'Llama 3.1 405B Instruct' },
                        { value: 'meta-llama/llama-3.1-8b-instruct', text: 'Llama 3.1 8B Instruct' },
                        { value: 'meta-llama/llama-3.1-70b-instruct', text: 'Llama 3.1 70B Instruct' },
                        { value: 'meta-llama/llama-3.2-11b-vision-instruct', text: 'Llama 3.2 11B Vision Instruct' },
                        { value: 'meta-llama/llama-3.2-90b-vision-instruct', text: 'Llama 3.2 90B Vision Instruct' },
                        { value: 'meta-llama/llama-3.2-1b-instruct', text: 'Llama 3.2 1B Instruct' },
                        { value: 'meta-llama/llama-3.2-3b-instruct', text: 'Llama 3.2 3B Instruct' },
                        { value: 'meta-llama/llama-3.3-70b-instruct', text: 'Llama 3.3 70B Instruct' },
                        { value: 'openrouter:meta-llama/llama-3-8b-instruct', text: 'Llama 3 8B (OpenRouter)' },
                        { value: 'openrouter:meta-llama/llama-3-70b-instruct', text: 'Llama 3 70B (OpenRouter)' },
                        { value: 'openrouter:meta-llama/llama-3.1-405b-instruct', text: 'Llama 3.1 405B Instruct (OpenRouter)' },
                        { value: 'openrouter:meta-llama/llama-3.1-405b-instruct:free', text: 'Llama 3.1 405B Instruct Free (OpenRouter)' },
                        { value: 'openrouter:meta-llama/llama-3.1-70b-instruct', text: 'Llama 3.1 70B Instruct (OpenRouter)' },
                        { value: 'openrouter:meta-llama/llama-3.1-70b-instruct:free', text: 'Llama 3.1 70B Instruct Free (OpenRouter)' },
                        { value: 'openrouter:meta-llama/llama-3.1-8b-instruct', text: 'Llama 3.1 8B Instruct (OpenRouter)' },
                        { value: 'openrouter:meta-llama/llama-3.1-8b-instruct:free', text: 'Llama 3.1 8B Instruct Free (OpenRouter)' },
                        { value: 'openrouter:meta-llama/llama-3.2-11b-vision-instruct', text: 'Llama 3.2 11B Vision Instruct (OpenRouter)' },
                        { value: 'openrouter:meta-llama/llama-3.2-11b-vision-instruct:free', text: 'Llama 3.2 11B Vision Instruct Free (OpenRouter)' },
                        { value: 'openrouter:meta-llama/llama-3.2-1b-instruct', text: 'Llama 3.2 1B Instruct (OpenRouter)' },
                        { value: 'openrouter:meta-llama/llama-3.2-1b-instruct:free', text: 'Llama 3.2 1B Instruct Free (OpenRouter)' },
                        { value: 'openrouter:meta-llama/llama-3.2-3b-instruct', text: 'Llama 3.2 3B Instruct (OpenRouter)' },
                        { value: 'openrouter:meta-llama/llama-3.2-3b-instruct:free', text: 'Llama 3.2 3B Instruct Free (OpenRouter)' },
                        { value: 'openrouter:meta-llama/llama-3.2-90b-vision-instruct', text: 'Llama 3.2 90B Vision Instruct (OpenRouter)' },
                        { value: 'openrouter:meta-llama/llama-3.3-70b-instruct', text: 'Llama 3.3 70B Instruct (OpenRouter)' },
                        { value: 'openrouter:meta-llama/llama-4-maverick', text: 'Llama 4 Maverick (OpenRouter)' },
                        { value: 'openrouter:meta-llama/llama-4-scout', text: 'Llama 4 Scout (OpenRouter)' }
                    ],
                    mistral: [
                        { value: 'mistralai/mistral-large-2501', text: 'Mistral Large 2501' },
                        { value: 'mistralai/mistral-small-2501', text: 'Mistral Small 2501' },
                        { value: 'mistralai/mistral-nemo-2501', text: 'Mistral Nemo 2501' },
                        { value: 'mistralai/codestral-2501', text: 'Codestral 2501' },
                        { value: 'mistralai/ministral-3b', text: 'Ministral 3B' },
                        { value: 'mistralai/ministral-8b', text: 'Ministral 8B' },
                        { value: 'openrouter:mistralai/mistral-7b-instruct', text: 'Mistral 7B Instruct (OpenRouter)' },
                        { value: 'openrouter:mistralai/mistral-7b-instruct:free', text: 'Mistral 7B Instruct Free (OpenRouter)' },
                        { value: 'openrouter:mistralai/mistral-large', text: 'Mistral Large (OpenRouter)' },
                        { value: 'openrouter:mistralai/mistral-large-2407', text: 'Mistral Large 2407 (OpenRouter)' },
                        { value: 'openrouter:mistralai/mistral-large-2411', text: 'Mistral Large 2411 (OpenRouter)' },
                        { value: 'openrouter:mistralai/mistral-medium-3', text: 'Mistral Medium 3 (OpenRouter)' },
                        { value: 'openrouter:mistralai/mistral-nemo', text: 'Mistral Nemo (OpenRouter)' },
                        { value: 'openrouter:mistralai/mistral-nemo:free', text: 'Mistral Nemo Free (OpenRouter)' },
                        { value: 'openrouter:mistralai/mistral-small', text: 'Mistral Small (OpenRouter)' },
                        { value: 'openrouter:mistralai/mistral-small-2402', text: 'Mistral Small 2402 (OpenRouter)' },
                        { value: 'openrouter:mistralai/mistral-small-2409', text: 'Mistral Small 2409 (OpenRouter)' },
                        { value: 'openrouter:mistralai/mistral-small-latest', text: 'Mistral Small Latest (OpenRouter)' },
                        { value: 'openrouter:mistralai/mixtral-8x7b-instruct', text: 'Mixtral 8x7B Instruct (OpenRouter)' },
                        { value: 'openrouter:mistralai/mixtral-8x7b-instruct:nitro', text: 'Mixtral 8x7B Instruct Nitro (OpenRouter)' },
                        { value: 'openrouter:mistralai/mixtral-8x22b-instruct', text: 'Mixtral 8x22B Instruct (OpenRouter)' },
                        { value: 'openrouter:mistralai/codestral-2501', text: 'Codestral 2501 (OpenRouter)' },
                        { value: 'openrouter:mistralai/codestral-latest', text: 'Codestral Latest (OpenRouter)' },
                        { value: 'openrouter:mistralai/codestral-mamba', text: 'Codestral Mamba (OpenRouter)' },
                        { value: 'openrouter:mistralai/ministral-3b', text: 'Ministral 3B (OpenRouter)' },
                        { value: 'openrouter:mistralai/ministral-8b', text: 'Ministral 8B (OpenRouter)' },
                        { value: 'openrouter:mistralai/pixtral-12b', text: 'Pixtral 12B (OpenRouter)' },
                        { value: 'openrouter:mistralai/pixtral-large', text: 'Pixtral Large (OpenRouter)' },
                        { value: 'openrouter:mistralai/devstral-medium', text: 'Devstral Medium (OpenRouter)' },
                        { value: 'openrouter:mistralai/devstral-small', text: 'Devstral Small (OpenRouter)' },
                        { value: 'openrouter:mistralai/devstral-small-2505', text: 'Devstral Small 2505 (OpenRouter)' },
                        { value: 'openrouter:mistralai/devstral-small-2505:free', text: 'Devstral Small 2505 Free (OpenRouter)' },
                        { value: 'openrouter:mistralai/mistral-small-3.1-24b-instruct', text: 'Mistral Small 3.1 24B Instruct (OpenRouter)' },
                        { value: 'openrouter:mistralai/mistral-small-3.1-24b-instruct:free', text: 'Mistral Small 3.1 24B Instruct Free (OpenRouter)' },
                        { value: 'openrouter:mistralai/mistral-small-3.2-24b-instruct', text: 'Mistral Small 3.2 24B Instruct (OpenRouter)' },
                        { value: 'openrouter:mistralai/mistral-small-3.2-24b-instruct:free', text: 'Mistral Small 3.2 24B Instruct Free (OpenRouter)' },
                        { value: 'openrouter:mistralai/mistral-small-24b-instruct-2501', text: 'Mistral Small 24B Instruct 2501 (OpenRouter)' },
                        { value: 'openrouter:mistralai/mistral-small-24b-instruct-2501:free', text: 'Mistral Small 24B Instruct 2501 Free (OpenRouter)' },
                        { value: 'openrouter:mistralai/magistral-small-2506', text: 'Magistral Small 2506 (OpenRouter)' },
                        { value: 'openrouter:mistralai/magistral-medium-2506', text: 'Magistral Medium 2506 (OpenRouter)' },
                        { value: 'openrouter:mistralai/magistral-medium-2506:thinking', text: 'Magistral Medium 2506 Thinking (OpenRouter)' },
                        { value: 'openrouter:mistralai/mistral-saba', text: 'Mistral Saba (OpenRouter)' }
                    ],
                    deepseek: [
                        { value: 'deepseek-chat', text: 'DeepSeek Chat' },
                        { value: 'deepseek-reasoner', text: 'DeepSeek Reasoner' },
                        { value: 'openrouter:deepseek/deepseek-chat', text: 'DeepSeek Chat (OpenRouter)' },
                        { value: 'openrouter:deepseek/deepseek-chat-v3-0324', text: 'DeepSeek Chat V3 (OpenRouter)' },
                        { value: 'openrouter:deepseek/deepseek-r1', text: 'DeepSeek R1 (OpenRouter)' },
                        { value: 'openrouter:deepseek/deepseek-r1:free', text: 'DeepSeek R1 Free (OpenRouter)' },
                        { value: 'openrouter:deepseek/deepseek-r1-0528', text: 'DeepSeek R1 0528 (OpenRouter)' },
                        { value: 'openrouter:deepseek/deepseek-r1-distill-llama-70b', text: 'DeepSeek R1 Distill Llama 70B (OpenRouter)' },
                        { value: 'openrouter:deepseek/deepseek-r1-distill-qwen-14b', text: 'DeepSeek R1 Distill Qwen 14B (OpenRouter)' },
                        { value: 'openrouter:deepseek/deepseek-r1-distill-qwen-32b', text: 'DeepSeek R1 Distill Qwen 32B (OpenRouter)' },
                        { value: 'openrouter:deepseek/deepseek-r1-distill-qwen-7b', text: 'DeepSeek R1 Distill Qwen 7B (OpenRouter)' },
                        { value: 'openrouter:deepseek/deepseek-prover-v2', text: 'DeepSeek Prover V2 (OpenRouter)' }
                    ],
                    qwen: [
                        { value: 'qwen/qwen-max', text: 'Qwen Max' },
                        { value: 'qwen/qwen-plus', text: 'Qwen Plus' },
                        { value: 'qwen/qwen-turbo', text: 'Qwen Turbo' },
                        { value: 'qwen/qwen-long', text: 'Qwen Long' },
                        { value: 'qwen/qwen2.5-72b-instruct', text: 'Qwen 2.5 72B Instruct' },
                        { value: 'qwen/qwen2.5-coder-32b-instruct', text: 'Qwen 2.5 Coder 32B Instruct' },
                        { value: 'qwen/qwq-32b-preview', text: 'QwQ 32B Preview' },
                        { value: 'openrouter:qwen/qwen-2-72b-instruct', text: 'Qwen 2 72B Instruct (OpenRouter)' },
                        { value: 'openrouter:qwen/qwen-2-vl-72b-instruct', text: 'Qwen 2 VL 72B Instruct (OpenRouter)' },
                        { value: 'openrouter:qwen/qwen-2.5-7b-instruct', text: 'Qwen 2.5 7B Instruct (OpenRouter)' },
                        { value: 'openrouter:qwen/qwen-2.5-7b-instruct:free', text: 'Qwen 2.5 7B Instruct Free (OpenRouter)' },
                        { value: 'openrouter:qwen/qwen-2.5-72b-instruct', text: 'Qwen 2.5 72B Instruct (OpenRouter)' },
                        { value: 'openrouter:qwen/qwen-2.5-coder-32b-instruct', text: 'Qwen 2.5 Coder 32B Instruct (OpenRouter)' },
                        { value: 'openrouter:qwen/qwen3-8b', text: 'Qwen 3 8B (OpenRouter)' },
                        { value: 'openrouter:qwen/qwen3-14b', text: 'Qwen 3 14B (OpenRouter)' },
                        { value: 'openrouter:qwen/qwen3-32b', text: 'Qwen 3 32B (OpenRouter)' },
                        { value: 'openrouter:qwen/qwq-32b', text: 'QwQ 32B (OpenRouter)' },
                        { value: 'openrouter:qwen/qwen-max', text: 'Qwen Max (OpenRouter)' },
                        { value: 'openrouter:qwen/qwen-plus', text: 'Qwen Plus (OpenRouter)' }
                    ],
                    cohere: [
                        { value: 'openrouter:cohere/command', text: 'Command (OpenRouter)' },
                        { value: 'openrouter:cohere/command-r', text: 'Command R (OpenRouter)' },
                        { value: 'openrouter:cohere/command-r-plus', text: 'Command R Plus (OpenRouter)' },
                        { value: 'openrouter:cohere/command-r7b-12-2024', text: 'Command R7B 12-2024 (OpenRouter)' }
                    ],
                    amazon: [
                        { value: 'openrouter:amazon/nova-lite-v1', text: 'Nova Lite V1 (OpenRouter)' },
                        { value: 'openrouter:amazon/nova-micro-v1', text: 'Nova Micro V1 (OpenRouter)' },
                        { value: 'openrouter:amazon/nova-pro-v1', text: 'Nova Pro V1 (OpenRouter)' }
                    ],
                    xai: [
                        { value: 'grok-2', text: 'Grok 2' },
                        { value: 'grok-2-vision', text: 'Grok 2 Vision' },
                        { value: 'grok-beta', text: 'Grok Beta' },
                        { value: 'openrouter:x-ai/grok-2-1212', text: 'Grok 2 1212 (OpenRouter)' },
                        { value: 'openrouter:x-ai/grok-2-vision-1212', text: 'Grok 2 Vision 1212 (OpenRouter)' },
                        { value: 'openrouter:x-ai/grok-3', text: 'Grok 3 (OpenRouter)' },
                        { value: 'openrouter:x-ai/grok-3-beta', text: 'Grok 3 Beta (OpenRouter)' },
                        { value: 'openrouter:x-ai/grok-3-mini', text: 'Grok 3 Mini (OpenRouter)' },
                        { value: 'openrouter:x-ai/grok-3-mini-beta', text: 'Grok 3 Mini Beta (OpenRouter)' }
                    ],
                    microsoft: [
                        { value: 'openrouter:microsoft/phi-3-mini-128k-instruct', text: 'Phi-3 Mini 128K Instruct (OpenRouter)' },
                        { value: 'openrouter:microsoft/phi-3-mini-128k-instruct:free', text: 'Phi-3 Mini 128K Instruct Free (OpenRouter)' },
                        { value: 'openrouter:microsoft/phi-3-medium-128k-instruct', text: 'Phi-3 Medium 128K Instruct (OpenRouter)' },
                        { value: 'openrouter:microsoft/phi-3.5-mini-128k-instruct', text: 'Phi-3.5 Mini 128K Instruct (OpenRouter)' },
                        { value: 'openrouter:microsoft/phi-4', text: 'Phi-4 (OpenRouter)' },
                        { value: 'openrouter:microsoft/phi-4-multimodal-instruct', text: 'Phi-4 Multimodal Instruct (OpenRouter)' },
                        { value: 'openrouter:microsoft/phi-4-reasoning-plus', text: 'Phi-4 Reasoning Plus (OpenRouter)' },
                        { value: 'openrouter:microsoft/wizardlm-2-8x22b', text: 'WizardLM 2 8x22B (OpenRouter)' }
                    ],
                    nvidia: [
                        { value: 'openrouter:nvidia/llama-3.1-nemotron-70b-instruct', text: 'Llama 3.1 Nemotron 70B Instruct (OpenRouter)' },
                        { value: 'openrouter:nvidia/llama-3.1-nemotron-ultra-253b-v1', text: 'Llama 3.1 Nemotron Ultra 253B V1 (OpenRouter)' },
                        { value: 'openrouter:nvidia/llama-3.3-nemotron-super-49b-v1', text: 'Llama 3.3 Nemotron Super 49B V1 (OpenRouter)' }
                    ],
                    other: [
                        { value: 'openrouter:perplexity/sonar', text: 'Perplexity Sonar (OpenRouter)' },
                        { value: 'openrouter:perplexity/sonar-pro', text: 'Perplexity Sonar Pro (OpenRouter)' },
                        { value: 'openrouter:perplexity/sonar-reasoning', text: 'Perplexity Sonar Reasoning (OpenRouter)' },
                        { value: 'openrouter:liquid/lfm-40b', text: 'Liquid LFM 40B (OpenRouter)' },
                        { value: 'openrouter:minimax/minimax-01', text: 'MiniMax 01 (OpenRouter)' },
                        { value: 'openrouter:inflection/inflection-3-pi', text: 'Inflection 3 Pi (OpenRouter)' },
                        { value: 'openrouter:ai21/jamba-1.6-large', text: 'AI21 Jamba 1.6 Large (OpenRouter)' },
                        { value: 'openrouter:ai21/jamba-1.6-mini', text: 'AI21 Jamba 1.6 Mini (OpenRouter)' },
                        { value: 'openrouter:arcee-ai/arcee-blitz', text: 'Arcee AI Blitz (OpenRouter)' },
                        { value: 'openrouter:arcee-ai/arcee-spark', text: 'Arcee AI Spark (OpenRouter)' },' },
                        { value: 'openrouter:teknium/openhermes-2.5-mistral-7b', text: 'OpenHermes 2.5 Mistral 7B (OpenRouter)' },
                        { value: 'openrouter:nousresearch/hermes-3-llama-3.1-405b', text: 'Hermes 3 Llama 3.1 405B (OpenRouter)' },
                        { value: 'openrouter:nousresearch/hermes-3-llama-3.1-405b:free', text: 'Hermes 3 Llama 3.1 405B Free (OpenRouter)' },
                        { value: 'openrouter:nousresearch/hermes-3-llama-3.1-70b', text: 'Hermes 3 Llama 3.1 70B (OpenRouter)' },
                        { value: 'openrouter:nousresearch/hermes-3-llama-3.1-8b', text: 'Hermes 3 Llama 3.1 8B (OpenRouter)' }
                    ]
                
                };
            }
            
            initializeElements() {
                this.providerSelect = document.getElementById('providerSelect');
                this.modelSelect = document.getElementById('modelSelect');
                this.customModelInput = document.getElementById('customModelInput');
                this.customModel = document.getElementById('customModel');
                this.chatMessages = document.getElementById('chatMessages');
                this.messageInput = document.getElementById('messageInput');
                this.sendBtn = document.getElementById('sendBtn');
                this.attachBtn = document.getElementById('attachBtn');
                this.fileInput = document.getElementById('fileInput');
                this.fileUploadArea = document.getElementById('fileUploadArea');
                this.filePreview = document.getElementById('filePreview');
                this.filePreviewList = document.getElementById('filePreviewList');
                this.saveHistoryBtn = document.getElementById('saveHistory');
                this.loadHistoryBtn = document.getElementById('loadHistory');
                this.clearHistoryBtn = document.getElementById('clearHistory');
                this.historyFileInput = document.getElementById('historyFileInput');
                this.messageCountSpan = document.getElementById('messageCount');
                this.currentModelSpan = document.getElementById('currentModel');
            }
            
            setupEventListeners() {
                // 发送消息
                this.sendBtn.addEventListener('click', () => this.sendMessage());
                this.messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });
                
                // 厂商选择
                this.providerSelect.addEventListener('change', () => {
                    this.updateModelOptions();
                });
                
                // 模型选择
                this.modelSelect.addEventListener('change', () => {
                    this.updateCurrentModel();
                });
                
                // 自定义模型输入
                this.customModel.addEventListener('input', () => {
                    this.updateCurrentModel();
                });
                
                this.customModel.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        this.updateCurrentModel();
                    }
                });
                
                // 文件上传
                this.attachBtn.addEventListener('click', () => {
                    this.fileUploadArea.classList.toggle('hidden');
                    if (!this.fileUploadArea.classList.contains('hidden')) {
                        this.fileInput.click();
                    }
                });
                
                this.fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
                
                // 拖拽上传
                this.fileUploadArea.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    this.fileUploadArea.classList.add('drag-over');
                });
                
                this.fileUploadArea.addEventListener('dragleave', () => {
                    this.fileUploadArea.classList.remove('drag-over');
                });
                
                this.fileUploadArea.addEventListener('drop', (e) => {
                    e.preventDefault();
                    this.fileUploadArea.classList.remove('drag-over');
                    this.handleFileSelect(e);
                });
                
                this.fileUploadArea.addEventListener('click', () => {
                    this.fileInput.click();
                });
                
                // 历史记录管理
                this.saveHistoryBtn.addEventListener('click', () => this.saveHistory());
                this.loadHistoryBtn.addEventListener('click', () => this.loadHistory());
                this.clearHistoryBtn.addEventListener('click', () => this.clearHistory());
                this.historyFileInput.addEventListener('change', (e) => this.handleHistoryFileLoad(e));
            }
            
            updateModelOptions() {
                const selectedProvider = this.providerSelect.value;
                
                // 清空模型选择
                this.modelSelect.innerHTML = '';
                
                if (!selectedProvider) {
                    this.modelSelect.innerHTML = '<option value="">请先选择厂商</option>';
                    this.modelSelect.disabled = true;
                    this.customModelInput.classList.add('hidden');
                    return;
                }
                
                // 检查是否选择了自定义模型
                if (selectedProvider === 'custom') {
                    this.modelSelect.innerHTML = '<option value="">使用下方自定义输入</option>';
                    this.modelSelect.disabled = true;
                    this.customModelInput.classList.remove('hidden');
                } else {
                    // 启用模型选择，隐藏自定义输入
                    this.modelSelect.disabled = false;
                    this.customModelInput.classList.add('hidden');
                    
                    // 添加默认选项
                    this.modelSelect.innerHTML = '<option value="">请选择模型</option>';
                    
                    // 添加对应厂商的模型
                    const models = this.modelData[selectedProvider];
                    if (models) {
                        models.forEach(model => {
                            const option = document.createElement('option');
                            option.value = model.value;
                            option.textContent = model.text;
                            this.modelSelect.appendChild(option);
                        });
                    }
                }
                
                // 更新当前模型显示
                this.updateCurrentModel();
            }
            
            handleFileSelect(e) {
                const files = e.target.files || e.dataTransfer.files;
                for (let file of files) {
                    this.currentFiles.push(file);
                    this.addFilePreview(file);
                }
                this.fileUploadArea.classList.add('hidden');
            }
            
            addFilePreview(file) {
                this.filePreview.classList.remove('hidden');
                
                const fileElement = document.createElement('div');
                fileElement.className = 'bg-blue-50 p-3 rounded-lg border border-blue-200 flex items-center space-x-2';
                
                const fileIcon = this.getFileIcon(file.type);
                const fileName = file.name.length > 20 ? file.name.substring(0, 20) + '...' : file.name;
                
                fileElement.innerHTML = `
                    <i class="fas ${fileIcon} text-blue-600"></i>
                    <span class="text-sm text-blue-700">${fileName}</span>
                    <button class="text-red-500 hover:text-red-700 ml-auto" onclick="this.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                
                this.filePreviewList.appendChild(fileElement);
            }
            
            getFileIcon(fileType) {
                if (fileType.startsWith('image/')) return 'fa-image';
                if (fileType.startsWith('video/')) return 'fa-video';
                if (fileType.startsWith('audio/')) return 'fa-music';
                if (fileType.includes('pdf')) return 'fa-file-pdf';
                if (fileType.includes('word')) return 'fa-file-word';
                if (fileType.includes('text')) return 'fa-file-text';
                return 'fa-file';
            }
            
            async sendMessage() {
                if (this.isProcessing) return;
                
                const message = this.messageInput.value.trim();
                if (!message && this.currentFiles.length === 0) return;
                
                // 获取当前模型
                let currentModel;
                const selectedProvider = this.providerSelect.value;
                
                if (selectedProvider === 'custom') {
                    currentModel = this.customModel.value.trim();
                    if (!currentModel) {
                        alert('请输入自定义模型名称');
                        return;
                    }
                } else {
                    currentModel = this.modelSelect.value;
                    if (!currentModel) {
                        alert('请先选择厂商和模型');
                        return;
                    }
                }
                
                this.isProcessing = true;
                this.sendBtn.disabled = true;
                
                // 添加用户消息
                this.addMessage('user', message, this.currentFiles);
                
                // 清空输入
                this.messageInput.value = '';
                this.currentFiles = [];
                this.filePreview.classList.add('hidden');
                this.filePreviewList.innerHTML = '';
                
                // 添加AI思考指示器
                const thinkingDiv = this.addThinkingIndicator();
                
                try {
                    // 准备消息历史
                    const messageHistory = this.messages.map(msg => ({
                        role: msg.role,
                        content: msg.content
                    }));
                    
                    // 调用AI API
                    const response = await puter.ai.chat(
                        messageHistory,
                        {
                            model: currentModel,
                            stream: true
                        }
                    );
                    
                    // 移除思考指示器
                    thinkingDiv.remove();
                    
                    // 创建AI回复消息
                    const aiMessageDiv = this.createMessageElement('assistant', '');
                    this.chatMessages.appendChild(aiMessageDiv);
                    
                    const contentDiv = aiMessageDiv.querySelector('.message-content');
                    let fullResponse = '';
                    
                    // 处理流式响应
                    for await (const part of response) {
                        if (part?.text) {
                            fullResponse += part.text;
                            contentDiv.innerHTML = this.formatMessage(fullResponse);
                            this.scrollToBottom();
                        }
                    }
                    
                    // 保存完整的AI回复
                    this.messages.push({
                        role: 'assistant',
                        content: fullResponse,
                        timestamp: new Date()
                    });
                    
                } catch (error) {
                    console.error('Error:', error);
                    thinkingDiv.remove();
                    this.addMessage('assistant', '抱歉，发生了错误。请检查网络连接或稍后重试。');
                }
                
                this.isProcessing = false;
                this.sendBtn.disabled = false;
                this.updateStats();
                this.saveToLocalStorage();
            }
            
            addMessage(role, content, files = []) {
                const message = {
                    role: role,
                    content: content,
                    files: files,
                    timestamp: new Date()
                };
                
                this.messages.push(message);
                
                const messageDiv = this.createMessageElement(role, content, files);
                this.chatMessages.appendChild(messageDiv);
                this.scrollToBottom();
            }
            
            createMessageElement(role, content, files = []) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message flex ${role === 'user' ? 'justify-end' : 'justify-start'}`;
                
                const isUser = role === 'user';
                const bgColor = isUser ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-800';
                const alignClass = isUser ? 'ml-12' : 'mr-12';
                
                let filesHtml = '';
                if (files && files.length > 0) {
                    filesHtml = `
                        <div class="mb-2">
                            ${files.map(file => `
                                <div class="inline-block bg-white bg-opacity-20 rounded px-2 py-1 text-xs mr-2 mb-1">
                                    <i class="fas ${this.getFileIcon(file.type)} mr-1"></i>
                                    ${file.name}
                                </div>
                            `).join('')}
                        </div>
                    `;
                }
                
                messageDiv.innerHTML = `
                    <div class="max-w-3xl ${alignClass}">
                        <div class="${bgColor} rounded-lg p-4">
                            <div class="flex items-center mb-2">
                                <i class="fas ${isUser ? 'fa-user' : 'fa-robot'} mr-2"></i>
                                <span class="font-medium">${isUser ? '用户' : 'AI助手'}</span>
                                <span class="text-xs opacity-75 ml-auto">${new Date().toLocaleTimeString()}</span>
                            </div>
                            ${filesHtml}
                            <div class="message-content">${this.formatMessage(content)}</div>
                        </div>
                    </div>
                `;
                
                return messageDiv;
            }
            
            addThinkingIndicator() {
                const thinkingDiv = document.createElement('div');
                thinkingDiv.className = 'message flex justify-start';
                thinkingDiv.innerHTML = `
                    <div class="max-w-3xl mr-12">
                        <div class="bg-gray-100 text-gray-800 rounded-lg p-4">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-robot mr-2"></i>
                                <span class="font-medium">AI助手</span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <span class="text-sm text-gray-600">正在思考</span>
                                <div class="typing-indicator"></div>
                                <div class="typing-indicator"></div>
                                <div class="typing-indicator"></div>
                            </div>
                        </div>
                    </div>
                `;
                
                this.chatMessages.appendChild(thinkingDiv);
                this.scrollToBottom();
                return thinkingDiv;
            }
            
            formatMessage(content) {
                // 首先转义HTML标签，防止被渲染
                let escapedContent = content
                    .replace(/&/g, '&amp;')
                    .replace(/</g, '&lt;')
                    .replace(/>/g, '&gt;')
                    .replace(/"/g, '&quot;')
                    .replace(/'/g, '&#39;');
                
                // 处理代码块（```语法）
                escapedContent = escapedContent.replace(/```(\w+)?\n([\s\S]*?)```/g, function(match, language, code) {
                    const lang = language || 'text';
                    return `<div class="bg-gray-900 text-gray-100 p-4 rounded-lg my-2 overflow-x-auto">
                        <div class="text-xs text-gray-400 mb-2">${lang}</div>
                        <pre><code>${code.trim()}</code></pre>
                    </div>`;
                });
                
                // 处理行内代码
                escapedContent = escapedContent.replace(/`([^`]+)`/g, '<code class="bg-gray-200 px-1 rounded text-sm">$1</code>');
                
                // 基本的markdown格式化
                escapedContent = escapedContent
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/\*(.*?)\*/g, '<em>$1</em>')
                    .replace(/\n/g, '<br>');
                
                return escapedContent;
            }
            
            scrollToBottom() {
                this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
            }
            
            updateStats() {
                this.messageCountSpan.textContent = this.messages.length;
            }
            
            updateCurrentModel() {
                const selectedProvider = this.providerSelect.value;
                
                if (selectedProvider === 'custom') {
                    const customModel = this.customModel.value.trim();
                    if (customModel) {
                        this.currentModelSpan.textContent = customModel;
                    } else {
                        this.currentModelSpan.textContent = '自定义模型 (未输入)';
                    }
                } else {
                    const selectedModel = this.modelSelect.value;
                    if (selectedModel) {
                        const selectedOption = this.modelSelect.options[this.modelSelect.selectedIndex];
                        this.currentModelSpan.textContent = selectedOption.text;
                    } else {
                        this.currentModelSpan.textContent = '未选择';
                    }
                }
            }
            
            saveHistory() {
                const historyData = {
                    messages: this.messages,
                    model: this.modelSelect.value,
                    provider: this.providerSelect.value,
                    customModel: this.customModel.value.trim(),
                    timestamp: new Date().toISOString()
                };
                
                const dataStr = JSON.stringify(historyData, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                
                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `chat_history_${new Date().toISOString().split('T')[0]}.json`;
                link.click();
            }
            
            loadHistory() {
                this.historyFileInput.click();
            }
            
            handleHistoryFileLoad(e) {
                const file = e.target.files[0];
                if (!file) return;
                
                const reader = new FileReader();
                reader.onload = (event) => {
                    try {
                        const historyData = JSON.parse(event.target.result);
                        this.messages = historyData.messages || [];
                        
                        if (historyData.provider) {
                            this.providerSelect.value = historyData.provider;
                            this.updateModelOptions();
                        }
                        
                        // 处理自定义模型
                        if (historyData.provider === 'custom' && historyData.customModel) {
                            this.customModel.value = historyData.customModel;
                        }
                        
                        if (historyData.model) {
                            this.modelSelect.value = historyData.model;
                        }
                        
                        this.updateCurrentModel();
                        this.renderMessages();
                        this.updateStats();
                    } catch (error) {
                        alert('加载历史记录失败，请检查文件格式');
                    }
                };
                reader.readAsText(file);
            }
            
            clearHistory() {
                if (confirm('确定要清空所有对话历史吗？')) {
                    this.messages = [];
                    this.chatMessages.innerHTML = `
                        <div class="text-center text-gray-500 py-8">
                            <i class="fas fa-comments text-4xl mb-4"></i>
                            <p>开始与AI对话吧！支持文本、图片、文档等多种输入方式。</p>
                        </div>
                    `;
                    this.updateStats();
                    this.saveToLocalStorage();
                }
            }
            
            renderMessages() {
                this.chatMessages.innerHTML = '';
                
                if (this.messages.length === 0) {
                    this.chatMessages.innerHTML = `
                        <div class="text-center text-gray-500 py-8">
                            <i class="fas fa-comments text-4xl mb-4"></i>
                            <p>开始与AI对话吧！支持文本、图片、文档等多种输入方式。</p>
                        </div>
                    `;
                    return;
                }
                
                this.messages.forEach(message => {
                    const messageDiv = this.createMessageElement(message.role, message.content, message.files);
                    this.chatMessages.appendChild(messageDiv);
                });
                
                this.scrollToBottom();
            }
            
            saveToLocalStorage() {
                try {
                    localStorage.setItem('chatHistory', JSON.stringify(this.messages));
                    localStorage.setItem('selectedModel', this.modelSelect.value);
                    localStorage.setItem('selectedProvider', this.providerSelect.value);
                } catch (error) {
                    console.warn('无法保存到本地存储');
                }
            }
            
            loadFromLocalStorage() {
                try {
                    const savedHistory = localStorage.getItem('chatHistory');
                    const savedModel = localStorage.getItem('selectedModel');
                    const savedProvider = localStorage.getItem('selectedProvider');
                    
                    if (savedHistory) {
                        this.messages = JSON.parse(savedHistory);
                        this.renderMessages();
                        this.updateStats();
                    }
                    
                    // 设置默认厂商和模型
                    if (savedProvider) {
                        this.providerSelect.value = savedProvider;
                        this.updateModelOptions();
                    } else {
                        // 设置默认厂商为OpenAI
                        this.providerSelect.value = 'openai';
                        this.updateModelOptions();
                    }
                    
                    if (savedModel) {
                        this.modelSelect.value = savedModel;
                        this.updateCurrentModel();
                    } else {
                        // 设置默认模型为GPT-4.1
                        this.modelSelect.value = 'gpt-4.1';
                        this.updateCurrentModel();
                    }
                } catch (error) {
                    console.warn('无法从本地存储加载数据');
                }
            }
        }
        
        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new ChatInterface();
        });
    </script>
</body>
</html>