<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI 大模型对话框</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        
        * {
            font-family: 'Inter', sans-serif;
        }
        
        .chat-container {
            max-height: 70vh;
            overflow-y: auto;
        }
        
        .message {
            animation: fadeIn 0.3s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .typing-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #3b82f6;
            animation: typing 1.4s infinite ease-in-out;
            margin-right: 4px;
        }
        
        .typing-indicator:nth-child(1) { animation-delay: -0.32s; }
        .typing-indicator:nth-child(2) { animation-delay: -0.16s; }
        
        @keyframes typing {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }
        
        .file-upload-area {
            border: 2px dashed #d1d5db;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .file-upload-area:hover {
            border-color: #3b82f6;
            background-color: #f8fafc;
        }
        
        .file-upload-area.drag-over {
            border-color: #3b82f6;
            background-color: #eff6ff;
        }
        
        .file-preview {
            max-width: 200px;
            max-height: 200px;
            object-fit: cover;
            border-radius: 8px;
        }
        
        .model-select {
            max-height: 200px;
            overflow-y: auto;
        }
        
        .scrollbar-thin {
            scrollbar-width: thin;
            scrollbar-color: #cbd5e1 #f1f5f9;
        }
        
        .scrollbar-thin::-webkit-scrollbar {
            width: 6px;
        }
        
        .scrollbar-thin::-webkit-scrollbar-track {
            background: #f1f5f9;
            border-radius: 3px;
        }
        
        .scrollbar-thin::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        
        .scrollbar-thin::-webkit-scrollbar-thumb:hover {
            background: #94a3b8;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <!-- 标题 -->
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">
                    <i class="fas fa-robot text-blue-600 mr-2"></i>
                    AI 大模型对话框
                </h1>
                <p class="text-gray-600">支持多种大模型的智能对话助手</p>
            </div>
            
            <!-- 控制面板 -->
            <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <!-- 模型选择 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-cog mr-1"></i>选择模型
                        </label>
                        <!-- 厂商选择 -->
                        <select id="providerSelect" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent mb-2">
                            <option value="">请选择厂商</option>
                            <option value="openai">OpenAI</option>
                            <option value="anthropic">Anthropic</option>
                            <option value="google">Google</option>
                            <option value="meta">Meta</option>
                            <option value="mistral">Mistral AI</option>
                            <option value="deepseek">DeepSeek</option>
                            <option value="qwen">Qwen</option>
                            <option value="cohere">Cohere</option>
                            <option value="amazon">Amazon</option>
                            <option value="xai">xAI</option>
                            <option value="microsoft">Microsoft</option>
                            <option value="nvidia">NVIDIA</option>
                            <option value="other">其他厂商</option>
                            <option value="custom">自定义模型</option>
                        </select>
                        <!-- 模型选择 -->
                        <select id="modelSelect" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent model-select scrollbar-thin mb-2" disabled>
                            <option value="">请先选择厂商</option>
                        </select>
                        <!-- 自定义模型输入 -->
                        <div id="customModelInput" class="hidden">
                            <input type="text" id="customModel" placeholder="输入自定义模型名称，如: openrouter:vendor/model-name" 
                                   class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm">
                            <div class="text-xs text-gray-500 mt-1">
                                <i class="fas fa-info-circle mr-1"></i>
                                支持格式：model-name 或 openrouter:vendor/model-name
                            </div>
                        </div>
                    </div>


                        </select>
                    </div>
                    
                    <!-- 历史记录管理 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-history mr-1"></i>历史记录
                        </label>
                        <div class="flex space-x-2">
                            <button id="saveHistory" class="flex-1 bg-green-600 text-white p-2 rounded-lg hover:bg-green-700 transition-colors">
                                <i class="fas fa-save mr-1"></i>保存
                            </button>
                            <button id="loadHistory" class="flex-1 bg-blue-600 text-white p-2 rounded-lg hover:bg-blue-700 transition-colors">
                                <i class="fas fa-upload mr-1"></i>加载
                            </button>
                            <button id="clearHistory" class="flex-1 bg-red-600 text-white p-2 rounded-lg hover:bg-red-700 transition-colors">
                                <i class="fas fa-trash mr-1"></i>清空
                            </button>
                        </div>
                    </div>
                    
                    <!-- 统计信息 -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-chart-bar mr-1"></i>对话统计
                        </label>
                        <div class="bg-gray-50 p-3 rounded-lg">
                            <div class="text-sm text-gray-600">
                                <div>消息数量: <span id="messageCount" class="font-medium text-blue-600">0</span></div>
                                <div>当前模型: <span id="currentModel" class="font-medium text-green-600">GPT-4o</span></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 对话区域 -->
            <div class="bg-white rounded-lg shadow-lg mb-6">
                <div class="p-6">
                    <div id="chatMessages" class="chat-container scrollbar-thin space-y-4">
                        <div class="text-center text-gray-500 py-8">
                            <i class="fas fa-comments text-4xl mb-4"></i>
                            <p>开始与AI对话吧！支持文本、图片、文档等多种输入方式。</p>
                        </div>
                    </div>
                </div>
                
                <!-- 输入区域 -->
                <div class="border-t p-6">
                    <!-- 文件上传区域 -->
                    <div id="fileUploadArea" class="file-upload-area mb-4 hidden">
                        <i class="fas fa-cloud-upload-alt text-2xl text-gray-400 mb-2"></i>
                        <p class="text-gray-600">拖拽文件到这里或点击上传</p>
                        <p class="text-sm text-gray-400">支持图片、文档、视频等多种格式</p>
                        <input type="file" id="fileInput" class="hidden" multiple accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt,.json,.csv">
                    </div>
                    
                    <!-- 已上传文件预览 -->
                    <div id="filePreview" class="mb-4 hidden">
                        <div class="flex flex-wrap gap-2" id="filePreviewList"></div>
                    </div>
                    
                    <!-- 消息输入 -->
                    <div class="flex space-x-4">
                        <button id="attachBtn" class="bg-gray-100 text-gray-600 p-3 rounded-lg hover:bg-gray-200 transition-colors">
                            <i class="fas fa-paperclip"></i>
                        </button>
                        <div class="flex-1 relative">
                            <textarea id="messageInput" 
                                placeholder="输入您的消息..." 
                                class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                                rows="3"></textarea>
                        </div>
                        <button id="sendBtn" class="bg-blue-600 text-white p-3 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 隐藏的文件输入用于加载历史记录 -->
    <input type="file" id="historyFileInput" class="hidden" accept=".json">
    
    <script src="https://js.puter.com/v2/"></script>
    <script>
        class ChatInterface {
            constructor() {
                this.messages = [];
                this.currentFiles = [];
                this.isProcessing = false;
                
                this.initializeElements();
                this.setupEventListeners();
                this.setupModelData();
                this.loadFromLocalStorage();
            }
            
            setupModelData() {
                this.modelData = {
    openai: [
        { value: 'gpt-4o', text: 'GPT 4o' },
        { value: 'gpt-4o-mini', text: 'GPT 4o mini' },
        { value: 'o1', text: 'o1' },
        { value: 'o1-mini', text: 'o1 mini' },
        { value: 'o1-pro', text: 'o1 pro' },
        { value: 'o3', text: 'o3' },
        { value: 'o3-mini', text: 'o3 mini' },
        { value: 'o4-mini', text: 'o4 mini' },
        { value: 'gpt-4.1', text: 'GPT 4.1' },
        { value: 'gpt-4.1-mini', text: 'GPT 4.1 mini' },
        { value: 'gpt-4.1-nano', text: 'GPT 4.1 nano' },
        { value: 'gpt-4.5-preview', text: 'GPT 4.5 preview' },
        { value: 'openrouter:openai/o3-pro', text: 'openai o3 pro (OpenRouter)' },
        { value: 'openrouter:openai/codex-mini', text: 'openai codex mini (OpenRouter)' },
        { value: 'openrouter:openai/o4-mini-high', text: 'openai o4 mini high (OpenRouter)' },
        { value: 'openrouter:openai/o3', text: 'openai o3 (OpenRouter)' },
        { value: 'openrouter:openai/o4-mini', text: 'openai o4 mini (OpenRouter)' },
        { value: 'openrouter:openai/gpt-4.1', text: 'openai GPT 4.1 (OpenRouter)' },
        { value: 'openrouter:openai/gpt-4.1-mini', text: 'openai GPT 4.1 mini (OpenRouter)' },
        { value: 'openrouter:openai/gpt-4.1-nano', text: 'openai GPT 4.1 nano (OpenRouter)' },
        { value: 'openrouter:openai/o1-pro', text: 'openai o1 pro (OpenRouter)' },
        { value: 'openrouter:openai/gpt-4o-mini-search-preview', text: 'openai GPT 4o mini search preview (OpenRouter)' },
        { value: 'openrouter:openai/gpt-4o-search-preview', text: 'openai GPT 4o search preview (OpenRouter)' },
        { value: 'openrouter:openai/gpt-4.5-preview', text: 'openai GPT 4.5 preview (OpenRouter)' },
        { value: 'openrouter:openai/o3-mini-high', text: 'openai o3 mini high (OpenRouter)' },
        { value: 'openrouter:openai/o3-mini', text: 'openai o3 mini (OpenRouter)' },
        { value: 'openrouter:openai/o1', text: 'openai o1 (OpenRouter)' },
        { value: 'openrouter:openai/gpt-4o-2024-11-20', text: 'openai GPT 4o 2024 11 20 (OpenRouter)' },
        { value: 'openrouter:openai/o1-preview', text: 'openai o1 preview (OpenRouter)' },
        { value: 'openrouter:openai/o1-preview-2024-09-12', text: 'openai o1 preview 2024 09 12 (OpenRouter)' },
        { value: 'openrouter:openai/o1-mini', text: 'openai o1 mini (OpenRouter)' },
        { value: 'openrouter:openai/o1-mini-2024-09-12', text: 'openai o1 mini 2024 09 12 (OpenRouter)' },
        { value: 'openrouter:openai/chatgpt-4o-latest', text: 'openai chatGPT 4o latest (OpenRouter)' },
        { value: 'openrouter:openai/gpt-4o-2024-08-06', text: 'openai GPT 4o 2024 08 06 (OpenRouter)' },
        { value: 'openrouter:openai/gpt-4o-mini', text: 'openai GPT 4o mini (OpenRouter)' },
        { value: 'openrouter:openai/gpt-4o-mini-2024-07-18', text: 'openai GPT 4o mini 2024 07 18 (OpenRouter)' },
        { value: 'openrouter:openai/gpt-4o', text: 'openai GPT 4o (OpenRouter)' },
        { value: 'openrouter:openai/gpt-4o:extended', text: 'openai GPT 4o:extended (OpenRouter)' },
        { value: 'openrouter:openai/gpt-4o-2024-05-13', text: 'openai GPT 4o 2024 05 13 (OpenRouter)' },
        { value: 'openrouter:openai/gpt-4-turbo', text: 'openai GPT 4 turbo (OpenRouter)' },
        { value: 'openrouter:openai/gpt-3.5-turbo-0613', text: 'openai GPT 3.5 turbo 0613 (OpenRouter)' },
        { value: 'openrouter:openai/gpt-4-turbo-preview', text: 'openai GPT 4 turbo preview (OpenRouter)' },
        { value: 'openrouter:openai/gpt-4-1106-preview', text: 'openai GPT 4 1106 preview (OpenRouter)' },
        { value: 'openrouter:openai/gpt-3.5-turbo-instruct', text: 'openai GPT 3.5 turbo instruct (OpenRouter)' },
        { value: 'openrouter:openai/gpt-3.5-turbo-16k', text: 'openai GPT 3.5 turbo 16k (OpenRouter)' },
        { value: 'openrouter:openai/gpt-4', text: 'openai GPT 4 (OpenRouter)' },
        { value: 'openrouter:openai/gpt-4-0314', text: 'openai GPT 4 0314 (OpenRouter)' }
    ],
    anthropic: [
        { value: 'claude-opus-4-20250514', text: 'Claude opus 4 20250514' },
        { value: 'claude-opus-4', text: 'Claude opus 4' },
        { value: 'claude-opus-4-latest', text: 'Claude opus 4 latest' },
        { value: 'claude-sonnet-4-20250514', text: 'Claude sonnet 4 20250514' },
        { value: 'claude-sonnet-4', text: 'Claude sonnet 4' },
        { value: 'claude-sonnet-4-latest', text: 'Claude sonnet 4 latest' },
        { value: 'claude-3-7-sonnet-20250219', text: 'Claude 3 7 sonnet 20250219' },
        { value: 'claude-3-7-sonnet-latest', text: 'Claude 3 7 sonnet latest' },
        { value: 'claude-3-5-sonnet-20241022', text: 'Claude 3 5 sonnet 20241022' },
        { value: 'claude-3-5-sonnet-latest', text: 'Claude 3 5 sonnet latest' },
        { value: 'claude-3-5-sonnet-20240620', text: 'Claude 3 5 sonnet 20240620' },
        { value: 'claude-3-haiku-20240307', text: 'Claude 3 haiku 20240307' },
        { value: 'openrouter:anthropic/claude-opus-4', text: 'anthropic Claude opus 4 (OpenRouter)' },
        { value: 'openrouter:anthropic/claude-sonnet-4', text: 'anthropic Claude sonnet 4 (OpenRouter)' },
        { value: 'openrouter:anthropic/claude-3.7-sonnet', text: 'anthropic Claude 3.7 sonnet (OpenRouter)' },
        { value: 'openrouter:anthropic/claude-3.7-sonnet:thinking', text: 'anthropic Claude 3.7 sonnet:thinking (OpenRouter)' },
        { value: 'openrouter:anthropic/claude-3.7-sonnet:beta', text: 'anthropic Claude 3.7 sonnet:beta (OpenRouter)' },
        { value: 'openrouter:anthropic/claude-3.5-haiku:beta', text: 'anthropic Claude 3.5 haiku:beta (OpenRouter)' },
        { value: 'openrouter:anthropic/claude-3.5-haiku', text: 'anthropic Claude 3.5 haiku (OpenRouter)' },
        { value: 'openrouter:anthropic/claude-3.5-haiku-20241022:beta', text: 'anthropic Claude 3.5 haiku 20241022:beta (OpenRouter)' },
        { value: 'openrouter:anthropic/claude-3.5-haiku-20241022', text: 'anthropic Claude 3.5 haiku 20241022 (OpenRouter)' },
        { value: 'openrouter:anthropic/claude-3.5-sonnet:beta', text: 'anthropic Claude 3.5 sonnet:beta (OpenRouter)' },
        { value: 'openrouter:anthropic/claude-3.5-sonnet', text: 'anthropic Claude 3.5 sonnet (OpenRouter)' },
        { value: 'openrouter:anthropic/claude-3.5-sonnet-20240620:beta', text: 'anthropic Claude 3.5 sonnet 20240620:beta (OpenRouter)' },
        { value: 'openrouter:anthropic/claude-3.5-sonnet-20240620', text: 'anthropic Claude 3.5 sonnet 20240620 (OpenRouter)' },
        { value: 'openrouter:anthropic/claude-3-haiku:beta', text: 'anthropic Claude 3 haiku:beta (OpenRouter)' },
        { value: 'openrouter:anthropic/claude-3-haiku', text: 'anthropic Claude 3 haiku (OpenRouter)' },
        { value: 'openrouter:anthropic/claude-3-opus:beta', text: 'anthropic Claude 3 opus:beta (OpenRouter)' },
        { value: 'openrouter:anthropic/claude-3-opus', text: 'anthropic Claude 3 opus (OpenRouter)' },
        { value: 'openrouter:anthropic/claude-3-sonnet:beta', text: 'anthropic Claude 3 sonnet:beta (OpenRouter)' },
        { value: 'openrouter:anthropic/claude-3-sonnet', text: 'anthropic Claude 3 sonnet (OpenRouter)' },
        { value: 'openrouter:anthropic/claude-2.1:beta', text: 'anthropic Claude 2.1:beta (OpenRouter)' },
        { value: 'openrouter:anthropic/claude-2.1', text: 'anthropic Claude 2.1 (OpenRouter)' },
        { value: 'openrouter:anthropic/claude-2:beta', text: 'anthropic Claude 2:beta (OpenRouter)' },
        { value: 'openrouter:anthropic/claude-2', text: 'anthropic Claude 2 (OpenRouter)' },
        { value: 'openrouter:anthropic/claude-2.0:beta', text: 'anthropic Claude 2.0:beta (OpenRouter)' },
        { value: 'openrouter:anthropic/claude-2.0', text: 'anthropic Claude 2.0 (OpenRouter)' }
    ],
    google: [
        { value: 'google/gemma-3-27b-it', text: 'google gemma 3 27b it' },
        { value: 'google/gemma-2-27b-it', text: 'google gemma 2 27b it' },
        { value: 'google/gemma-3n-E4B-it', text: 'google gemma 3n E4B it' },
        { value: 'gemini-1.5-flash', text: 'Gemini 1.5 flash' },
        { value: 'gemini-2.0-flash', text: 'Gemini 2.0 flash' },
        { value: 'openrouter:google/gemma-3n-e2b-it:free', text: 'google gemma 3n e2b it:free (OpenRouter)' },
        { value: 'openrouter:google/gemini-2.5-flash-lite-preview-06-17', text: 'google Gemini 2.5 flash lite preview 06 17 (OpenRouter)' },
        { value: 'openrouter:google/gemini-2.5-flash', text: 'google Gemini 2.5 flash (OpenRouter)' },
        { value: 'openrouter:google/gemini-2.5-pro', text: 'google Gemini 2.5 pro (OpenRouter)' },
        { value: 'openrouter:google/gemini-2.5-pro-preview', text: 'google Gemini 2.5 pro preview (OpenRouter)' },
        { value: 'openrouter:google/gemma-3n-e4b-it:free', text: 'google gemma 3n e4b it:free (OpenRouter)' },
        { value: 'openrouter:google/gemma-3n-e4b-it', text: 'google gemma 3n e4b it (OpenRouter)' },
        { value: 'openrouter:google/gemini-2.5-flash-preview-05-20', text: 'google Gemini 2.5 flash preview 05 20 (OpenRouter)' },
        { value: 'openrouter:google/gemini-2.5-flash-preview-05-20:thinking', text: 'google Gemini 2.5 flash preview 05 20:thinking (OpenRouter)' },
        { value: 'openrouter:google/gemini-2.5-pro-preview-05-06', text: 'google Gemini 2.5 pro preview 05 06 (OpenRouter)' },
        { value: 'openrouter:google/gemini-2.5-flash-preview', text: 'google Gemini 2.5 flash preview (OpenRouter)' },
        { value: 'openrouter:google/gemini-2.5-flash-preview:thinking', text: 'google Gemini 2.5 flash preview:thinking (OpenRouter)' },
        { value: 'openrouter:google/gemini-2.5-pro-exp-03-25', text: 'google Gemini 2.5 pro exp 03 25 (OpenRouter)' },
        { value: 'openrouter:google/gemma-3-4b-it:free', text: 'google gemma 3 4b it:free (OpenRouter)' },
        { value: 'openrouter:google/gemma-3-4b-it', text: 'google gemma 3 4b it (OpenRouter)' },
        { value: 'openrouter:google/gemma-3-12b-it:free', text: 'google gemma 3 12b it:free (OpenRouter)' },
        { value: 'openrouter:google/gemma-3-12b-it', text: 'google gemma 3 12b it (OpenRouter)' },
        { value: 'openrouter:google/gemma-3-27b-it:free', text: 'google gemma 3 27b it:free (OpenRouter)' },
        { value: 'openrouter:google/gemma-3-27b-it', text: 'google gemma 3 27b it (OpenRouter)' },
        { value: 'openrouter:google/gemini-2.0-flash-lite-001', text: 'google Gemini 2.0 flash lite 001 (OpenRouter)' },
        { value: 'openrouter:google/gemini-2.0-flash-001', text: 'google Gemini 2.0 flash 001 (OpenRouter)' },
        { value: 'openrouter:google/gemini-2.0-flash-exp:free', text: 'google Gemini 2.0 flash exp:free (OpenRouter)' },
        { value: 'openrouter:google/gemini-flash-1.5-8b', text: 'google Gemini flash 1.5 8b (OpenRouter)' },
        { value: 'openrouter:google/gemma-2-27b-it', text: 'google gemma 2 27b it (OpenRouter)' },
        { value: 'openrouter:google/gemma-2-9b-it:free', text: 'google gemma 2 9b it:free (OpenRouter)' },
        { value: 'openrouter:google/gemma-2-9b-it', text: 'google gemma 2 9b it (OpenRouter)' },
        { value: 'openrouter:google/gemini-flash-1.5', text: 'google Gemini flash 1.5 (OpenRouter)' },
        { value: 'openrouter:google/gemini-pro-1.5', text: 'google Gemini pro 1.5 (OpenRouter)' }
    ],
    meta: [
        { value: 'meta-llama/Llama-3.3-70B-Instruct-Turbo', text: 'meta Llama Llama 3.3 70B Instruct Turbo' },
        { value: 'meta-llama/Meta-Llama-Guard-3-8B', text: 'meta Llama Meta Llama Guard 3 8B' },
        { value: 'meta-llama/Llama-Guard-3-11B-Vision-Turbo', text: 'meta Llama Llama Guard 3 11B Vision Turbo' },
        { value: 'meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo', text: 'meta Llama Meta Llama 3.1 8B Instruct Turbo' },
        { value: 'meta-llama/Meta-Llama-3-8B-Instruct-Lite', text: 'meta Llama Meta Llama 3 8B Instruct Lite' },
        { value: 'meta-llama/Llama-2-70b-hf', text: 'meta Llama Llama 2 70b hf' },
        { value: 'meta-llama/Llama-3.2-90B-Vision-Instruct-Turbo', text: 'meta Llama Llama 3.2 90B Vision Instruct Turbo' },
        { value: 'meta-llama/Meta-Llama-3.1-405B-Instruct-Turbo', text: 'meta Llama Meta Llama 3.1 405B Instruct Turbo' },
        { value: 'meta-llama/Meta-Llama-3-70B-Instruct-Turbo', text: 'meta Llama Meta Llama 3 70B Instruct Turbo' },
        { value: 'meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8', text: 'meta Llama Llama 4 Maverick 17B 128E Instruct FP8' },
        { value: 'meta-llama/Llama-Guard-4-12B', text: 'meta Llama Llama Guard 4 12B' },
        { value: 'meta-llama/Llama-Vision-Free', text: 'meta Llama Llama Vision Free' },
        { value: 'meta-llama/Llama-3.2-3B-Instruct-Turbo', text: 'meta Llama Llama 3.2 3B Instruct Turbo' },
        { value: 'meta-llama/LlamaGuard-2-8b', text: 'meta Llama LlamaGuard 2 8b' },
        { value: 'meta-llama/Llama-3.2-11B-Vision-Instruct-Turbo', text: 'meta Llama Llama 3.2 11B Vision Instruct Turbo' },
        { value: 'meta-llama/Llama-3-70b-chat-hf', text: 'meta Llama Llama 3 70b chat hf' },
        { value: 'meta-llama/Llama-3.3-70B-Instruct-Turbo-Free', text: 'meta Llama Llama 3.3 70B Instruct Turbo Free' },
        { value: 'meta-llama/Llama-4-Scout-17B-16E-Instruct', text: 'meta Llama Llama 4 Scout 17B 16E Instruct' },
        { value: 'meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo', text: 'meta Llama Meta Llama 3.1 70B Instruct Turbo' },
        { value: 'meta-llama/Llama-3-8b-chat-hf', text: 'meta Llama Llama 3 8b chat hf' },
        { value: 'openrouter:meta-llama/llama-guard-4-12b', text: 'meta Llama Llama guard 4 12b (OpenRouter)' },
        { value: 'openrouter:meta-llama/llama-4-maverick:free', text: 'meta Llama Llama 4 maverick:free (OpenRouter)' },
        { value: 'openrouter:meta-llama/llama-4-maverick', text: 'meta Llama Llama 4 maverick (OpenRouter)' },
        { value: 'openrouter:meta-llama/llama-4-scout:free', text: 'meta Llama Llama 4 scout:free (OpenRouter)' },
        { value: 'openrouter:meta-llama/llama-4-scout', text: 'meta Llama Llama 4 scout (OpenRouter)' },
        { value: 'openrouter:meta-llama/llama-guard-3-8b', text: 'meta Llama Llama guard 3 8b (OpenRouter)' },
        { value: 'openrouter:meta-llama/llama-3.3-70b-instruct:free', text: 'meta Llama Llama 3.3 70b instruct:free (OpenRouter)' },
        { value: 'openrouter:meta-llama/llama-3.3-70b-instruct', text: 'meta Llama Llama 3.3 70b instruct (OpenRouter)' },
        { value: 'openrouter:meta-llama/llama-3.2-3b-instruct:free', text: 'meta Llama Llama 3.2 3b instruct:free (OpenRouter)' },
        { value: 'openrouter:meta-llama/llama-3.2-3b-instruct', text: 'meta Llama Llama 3.2 3b instruct (OpenRouter)' },
        { value: 'openrouter:meta-llama/llama-3.2-1b-instruct', text: 'meta Llama Llama 3.2 1b instruct (OpenRouter)' },
        { value: 'openrouter:meta-llama/llama-3.2-90b-vision-instruct', text: 'meta Llama Llama 3.2 90b vision instruct (OpenRouter)' },
        { value: 'openrouter:meta-llama/llama-3.2-11b-vision-instruct:free', text: 'meta Llama Llama 3.2 11b vision instruct:free (OpenRouter)' },
        { value: 'openrouter:meta-llama/llama-3.2-11b-vision-instruct', text: 'meta Llama Llama 3.2 11b vision instruct (OpenRouter)' },
        { value: 'openrouter:meta-llama/llama-3.1-405b', text: 'meta Llama Llama 3.1 405b (OpenRouter)' },
        { value: 'openrouter:meta-llama/llama-3.1-8b-instruct', text: 'meta Llama Llama 3.1 8b instruct (OpenRouter)' },
        { value: 'openrouter:meta-llama/llama-3.1-405b-instruct:free', text: 'meta Llama Llama 3.1 405b instruct:free (OpenRouter)' },
        { value: 'openrouter:meta-llama/llama-3.1-405b-instruct', text: 'meta Llama Llama 3.1 405b instruct (OpenRouter)' },
        { value: 'openrouter:meta-llama/llama-3.1-70b-instruct', text: 'meta Llama Llama 3.1 70b instruct (OpenRouter)' },
        { value: 'openrouter:meta-llama/llama-guard-2-8b', text: 'meta Llama Llama guard 2 8b (OpenRouter)' },
        { value: 'openrouter:meta-llama/llama-3-8b-instruct', text: 'meta Llama Llama 3 8b instruct (OpenRouter)' },
        { value: 'openrouter:meta-llama/llama-3-70b-instruct', text: 'meta Llama Llama 3 70b instruct (OpenRouter)' }
    ],
    mistral: [
        { value: 'mistralai/Mistral-Small-24B-Instruct-2501', text: 'Mistralai Mistral Small 24B Instruct 2501' },
        { value: 'mistralai/Mistral-7B-Instruct-v0.3', text: 'Mistralai Mistral 7B Instruct v0.3' },
        { value: 'mistralai/Mistral-7B-Instruct-v0.2', text: 'Mistralai Mistral 7B Instruct v0.2' },
        { value: 'mistralai/Mixtral-8x7B-Instruct-v0.1', text: 'Mistralai Mixtral 8x7B Instruct v0.1' },
        { value: 'mistralai/Mistral-7B-Instruct-v0.1', text: 'Mistralai Mistral 7B Instruct v0.1' },
        { value: 'mistral-medium-2505', text: 'Mistral medium 2505' },
        { value: 'mistral-medium-latest', text: 'Mistral medium latest' },
        { value: 'mistral-medium', text: 'Mistral medium' },
        { value: 'ministral-3b-2410', text: 'ministral 3b 2410' },
        { value: 'ministral-3b-latest', text: 'ministral 3b latest' },
        { value: 'ministral-8b-2410', text: 'ministral 8b 2410' },
        { value: 'ministral-8b-latest', text: 'ministral 8b latest' },
        { value: 'open-mistral-7b', text: 'open Mistral 7b' },
        { value: 'mistral-tiny', text: 'Mistral tiny' },
        { value: 'mistral-tiny-2312', text: 'Mistral tiny 2312' },
        { value: 'open-mistral-nemo', text: 'open Mistral nemo' },
        { value: 'open-mistral-nemo-2407', text: 'open Mistral nemo 2407' },
        { value: 'mistral-tiny-2407', text: 'Mistral tiny 2407' },
        { value: 'mistral-tiny-latest', text: 'Mistral tiny latest' },
        { value: 'open-mixtral-8x7b', text: 'open mixtral 8x7b' },
        { value: 'mistral-small', text: 'Mistral small' },
        { value: 'mistral-small-2312', text: 'Mistral small 2312' },
        { value: 'open-mixtral-8x22b', text: 'open mixtral 8x22b' },
        { value: 'open-mixtral-8x22b-2404', text: 'open mixtral 8x22b 2404' },
        { value: 'mistral-large-2411', text: 'Mistral large 2411' },
        { value: 'mistral-large-latest', text: 'Mistral large latest' },
        { value: 'pixtral-large-2411', text: 'pixtral large 2411' },
        { value: 'pixtral-large-latest', text: 'pixtral large latest' },
        { value: 'mistral-large-pixtral-2411', text: 'Mistral large pixtral 2411' },
        { value: 'codestral-2501', text: 'codestral 2501' },
        { value: 'codestral-latest', text: 'codestral latest' },
        { value: 'codestral-2412', text: 'codestral 2412' },
        { value: 'codestral-2411-rc5', text: 'codestral 2411 rc5' },
        { value: 'devstral-small-2507', text: 'devstral small 2507' },
        { value: 'devstral-small-latest', text: 'devstral small latest' },
        { value: 'pixtral-12b-2409', text: 'pixtral 12b 2409' },
        { value: 'pixtral-12b', text: 'pixtral 12b' },
        { value: 'pixtral-12b-latest', text: 'pixtral 12b latest' },
        { value: 'mistral-small-2506', text: 'Mistral small 2506' },
        { value: 'mistral-small-latest', text: 'Mistral small latest' },
        { value: 'mistral-saba-2502', text: 'Mistral saba 2502' },
        { value: 'mistral-saba-latest', text: 'Mistral saba latest' },
        { value: 'magistral-medium-2506', text: 'magistral medium 2506' },
        { value: 'magistral-medium-latest', text: 'magistral medium latest' },
        { value: 'magistral-small-2506', text: 'magistral small 2506' },
        { value: 'magistral-small-latest', text: 'magistral small latest' },
        { value: 'mistral-moderation-2411', text: 'Mistral moderation 2411' },
        { value: 'mistral-moderation-latest', text: 'Mistral moderation latest' },
        { value: 'mistral-ocr-2505', text: 'Mistral ocr 2505' },
        { value: 'mistral-ocr-latest', text: 'Mistral ocr latest' },
        { value: 'openrouter:mistralai/devstral-medium', text: 'Mistralai devstral medium (OpenRouter)' },
        { value: 'openrouter:mistralai/devstral-small', text: 'Mistralai devstral small (OpenRouter)' },
        { value: 'openrouter:mistralai/mistral-small-3.2-24b-instruct:free', text: 'Mistralai Mistral small 3.2 24b instruct:free (OpenRouter)' },
        { value: 'openrouter:mistralai/mistral-small-3.2-24b-instruct', text: 'Mistralai Mistral small 3.2 24b instruct (OpenRouter)' },
        { value: 'openrouter:mistralai/magistral-small-2506', text: 'Mistralai magistral small 2506 (OpenRouter)' },
        { value: 'openrouter:mistralai/magistral-medium-2506', text: 'Mistralai magistral medium 2506 (OpenRouter)' },
        { value: 'openrouter:mistralai/magistral-medium-2506:thinking', text: 'Mistralai magistral medium 2506:thinking (OpenRouter)' },
        { value: 'openrouter:mistralai/devstral-small-2505:free', text: 'Mistralai devstral small 2505:free (OpenRouter)' },
        { value: 'openrouter:mistralai/devstral-small-2505', text: 'Mistralai devstral small 2505 (OpenRouter)' },
        { value: 'openrouter:mistralai/mistral-medium-3', text: 'Mistralai Mistral medium 3 (OpenRouter)' },
        { value: 'openrouter:mistralai/mistral-small-3.1-24b-instruct:free', text: 'Mistralai Mistral small 3.1 24b instruct:free (OpenRouter)' },
        { value: 'openrouter:mistralai/mistral-small-3.1-24b-instruct', text: 'Mistralai Mistral small 3.1 24b instruct (OpenRouter)' },
        { value: 'openrouter:mistralai/mistral-saba', text: 'Mistralai Mistral saba (OpenRouter)' },
        { value: 'openrouter:mistralai/mistral-small-24b-instruct-2501:free', text: 'Mistralai Mistral small 24b instruct 2501:free (OpenRouter)' },
        { value: 'openrouter:mistralai/mistral-small-24b-instruct-2501', text: 'Mistralai Mistral small 24b instruct 2501 (OpenRouter)' },
        { value: 'openrouter:mistralai/codestral-2501', text: 'Mistralai codestral 2501 (OpenRouter)' },
        { value: 'openrouter:mistralai/mistral-large-2411', text: 'Mistralai Mistral large 2411 (OpenRouter)' },
        { value: 'openrouter:mistralai/mistral-large-2407', text: 'Mistralai Mistral large 2407 (OpenRouter)' },
        { value: 'openrouter:mistralai/pixtral-large-2411', text: 'Mistralai pixtral large 2411 (OpenRouter)' },
        { value: 'openrouter:mistralai/ministral-8b', text: 'Mistralai ministral 8b (OpenRouter)' },
        { value: 'openrouter:mistralai/ministral-3b', text: 'Mistralai ministral 3b (OpenRouter)' },
        { value: 'openrouter:mistralai/pixtral-12b', text: 'Mistralai pixtral 12b (OpenRouter)' },
        { value: 'openrouter:mistralai/mistral-nemo:free', text: 'Mistralai Mistral nemo:free (OpenRouter)' },
        { value: 'openrouter:mistralai/mistral-nemo', text: 'Mistralai Mistral nemo (OpenRouter)' },
        { value: 'openrouter:mistralai/mistral-7b-instruct:free', text: 'Mistralai Mistral 7b instruct:free (OpenRouter)' },
        { value: 'openrouter:mistralai/mistral-7b-instruct', text: 'Mistralai Mistral 7b instruct (OpenRouter)' },
        { value: 'openrouter:mistralai/mistral-7b-instruct-v0.3', text: 'Mistralai Mistral 7b instruct v0.3 (OpenRouter)' },
        { value: 'openrouter:mistralai/mixtral-8x22b-instruct', text: 'Mistralai mixtral 8x22b instruct (OpenRouter)' },
        { value: 'openrouter:mistralai/mistral-large', text: 'Mistralai Mistral large (OpenRouter)' },
        { value: 'openrouter:mistralai/mistral-small', text: 'Mistralai Mistral small (OpenRouter)' },
        { value: 'openrouter:mistralai/mistral-tiny', text: 'Mistralai Mistral tiny (OpenRouter)' },
        { value: 'openrouter:mistralai/mistral-7b-instruct-v0.2', text: 'Mistralai Mistral 7b instruct v0.2 (OpenRouter)' },
        { value: 'openrouter:mistralai/mixtral-8x7b-instruct', text: 'Mistralai mixtral 8x7b instruct (OpenRouter)' },
        { value: 'openrouter:mistralai/mistral-7b-instruct-v0.1', text: 'Mistralai Mistral 7b instruct v0.1 (OpenRouter)' }
    ],
    deepseek: [
        { value: 'deepseek-ai/DeepSeek-R1-Distill-Llama-70B', text: 'DeepSeek ai DeepSeek R1 Distill Llama 70B' },
        { value: 'deepseek-ai/DeepSeek-R1-Distill-Qwen-14B', text: 'DeepSeek ai DeepSeek R1 Distill Qwen 14B' },
        { value: 'deepseek-ai/DeepSeek-R1-Distill-Llama-70B-free', text: 'DeepSeek ai DeepSeek R1 Distill Llama 70B free' },
        { value: 'deepseek-ai/DeepSeek-R1', text: 'DeepSeek ai DeepSeek R1' },
        { value: 'deepseek-ai/DeepSeek-V3', text: 'DeepSeek ai DeepSeek V3' },
        { value: 'deepseek-ai/DeepSeek-V3-p-dp', text: 'DeepSeek ai DeepSeek V3 p dp' },
        { value: 'deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B', text: 'DeepSeek ai DeepSeek R1 Distill Qwen 1.5B' },
        { value: 'deepseek-ai/DeepSeek-R1-0528-tput', text: 'DeepSeek ai DeepSeek R1 0528 tput' },
        { value: 'deepseek-chat', text: 'DeepSeek chat' },
        { value: 'deepseek-reasoner', text: 'DeepSeek reasoner' },
        { value: 'openrouter:deepseek/deepseek-r1-distill-qwen-7b', text: 'DeepSeek DeepSeek r1 distill Qwen 7b (OpenRouter)' },
        { value: 'openrouter:deepseek/deepseek-r1-0528-qwen3-8b:free', text: 'DeepSeek DeepSeek r1 0528 Qwen3 8b:free (OpenRouter)' },
        { value: 'openrouter:deepseek/deepseek-r1-0528-qwen3-8b', text: 'DeepSeek DeepSeek r1 0528 Qwen3 8b (OpenRouter)' },
        { value: 'openrouter:deepseek/deepseek-r1-0528:free', text: 'DeepSeek DeepSeek r1 0528:free (OpenRouter)' },
        { value: 'openrouter:deepseek/deepseek-r1-0528', text: 'DeepSeek DeepSeek r1 0528 (OpenRouter)' },
        { value: 'openrouter:deepseek/deepseek-prover-v2', text: 'DeepSeek DeepSeek prover v2 (OpenRouter)' },
        { value: 'openrouter:deepseek/deepseek-v3-base:free', text: 'DeepSeek DeepSeek v3 base:free (OpenRouter)' },
        { value: 'openrouter:deepseek/deepseek-chat-v3-0324:free', text: 'DeepSeek DeepSeek chat v3 0324:free (OpenRouter)' },
        { value: 'openrouter:deepseek/deepseek-chat-v3-0324', text: 'DeepSeek DeepSeek chat v3 0324 (OpenRouter)' },
        { value: 'openrouter:deepseek/deepseek-r1-distill-llama-8b', text: 'DeepSeek DeepSeek r1 distill Llama 8b (OpenRouter)' },
        { value: 'openrouter:deepseek/deepseek-r1-distill-qwen-1.5b', text: 'DeepSeek DeepSeek r1 distill Qwen 1.5b (OpenRouter)' },
        { value: 'openrouter:deepseek/deepseek-r1-distill-qwen-32b', text: 'DeepSeek DeepSeek r1 distill Qwen 32b (OpenRouter)' },
        { value: 'openrouter:deepseek/deepseek-r1-distill-qwen-14b:free', text: 'DeepSeek DeepSeek r1 distill Qwen 14b:free (OpenRouter)' },
        { value: 'openrouter:deepseek/deepseek-r1-distill-qwen-14b', text: 'DeepSeek DeepSeek r1 distill Qwen 14b (OpenRouter)' },
        { value: 'openrouter:deepseek/deepseek-r1-distill-llama-70b:free', text: 'DeepSeek DeepSeek r1 distill Llama 70b:free (OpenRouter)' },
        { value: 'openrouter:deepseek/deepseek-r1-distill-llama-70b', text: 'DeepSeek DeepSeek r1 distill Llama 70b (OpenRouter)' },
        { value: 'openrouter:deepseek/deepseek-r1:free', text: 'DeepSeek DeepSeek r1:free (OpenRouter)' },
        { value: 'openrouter:deepseek/deepseek-r1', text: 'DeepSeek DeepSeek r1 (OpenRouter)' },
        { value: 'openrouter:deepseek/deepseek-chat:free', text: 'DeepSeek DeepSeek chat:free (OpenRouter)' },
        { value: 'openrouter:deepseek/deepseek-chat', text: 'DeepSeek DeepSeek chat (OpenRouter)' }
    ],
    qwen: [
        { value: 'Qwen/QwQ-32B', text: 'Qwen QwQ 32B' },
        { value: 'Qwen/Qwen2.5-VL-72B-Instruct', text: 'Qwen Qwen2.5 VL 72B Instruct' },
        { value: 'Qwen/Qwen2-VL-72B-Instruct', text: 'Qwen Qwen2 VL 72B Instruct' },
        { value: 'Qwen/Qwen3-32B-FP8', text: 'Qwen Qwen3 32B FP8' },
        { value: 'Qwen/Qwen3-235B-A22B-fp8-tput', text: 'Qwen Qwen3 235B A22B fp8 tput' },
        { value: 'Qwen/Qwen2.5-72B-Instruct-Turbo', text: 'Qwen Qwen2.5 72B Instruct Turbo' },
        { value: 'Qwen/Qwen2-72B-Instruct', text: 'Qwen Qwen2 72B Instruct' },
        { value: 'qwen-qwen3-32b-fp8-serverless', text: 'Qwen Qwen3 32b fp8 serverless' },
        { value: 'Qwen/Qwen2.5-Coder-32B-Instruct', text: 'Qwen Qwen2.5 Coder 32B Instruct' },
        { value: 'Qwen/Qwen2.5-7B-Instruct-Turbo', text: 'Qwen Qwen2.5 7B Instruct Turbo' },
        { value: 'openrouter:qwen/qwen3-4b:free', text: 'Qwen Qwen3 4b:free (OpenRouter)' },
        { value: 'openrouter:qwen/qwen3-30b-a3b:free', text: 'Qwen Qwen3 30b a3b:free (OpenRouter)' },
        { value: 'openrouter:qwen/qwen3-30b-a3b', text: 'Qwen Qwen3 30b a3b (OpenRouter)' },
        { value: 'openrouter:qwen/qwen3-8b:free', text: 'Qwen Qwen3 8b:free (OpenRouter)' },
        { value: 'openrouter:qwen/qwen3-8b', text: 'Qwen Qwen3 8b (OpenRouter)' },
        { value: 'openrouter:qwen/qwen3-14b:free', text: 'Qwen Qwen3 14b:free (OpenRouter)' },
        { value: 'openrouter:qwen/qwen3-14b', text: 'Qwen Qwen3 14b (OpenRouter)' },
        { value: 'openrouter:qwen/qwen3-32b:free', text: 'Qwen Qwen3 32b:free (OpenRouter)' },
        { value: 'openrouter:qwen/qwen3-32b', text: 'Qwen Qwen3 32b (OpenRouter)' },
        { value: 'openrouter:qwen/qwen3-235b-a22b:free', text: 'Qwen Qwen3 235b a22b:free (OpenRouter)' },
        { value: 'openrouter:qwen/qwen3-235b-a22b', text: 'Qwen Qwen3 235b a22b (OpenRouter)' },
        { value: 'openrouter:qwen/qwen2.5-vl-32b-instruct:free', text: 'Qwen Qwen2.5 vl 32b instruct:free (OpenRouter)' },
        { value: 'openrouter:qwen/qwen2.5-vl-32b-instruct', text: 'Qwen Qwen2.5 vl 32b instruct (OpenRouter)' },
        { value: 'openrouter:qwen/qwq-32b:free', text: 'Qwen qwq 32b:free (OpenRouter)' },
        { value: 'openrouter:qwen/qwq-32b', text: 'Qwen qwq 32b (OpenRouter)' },
        { value: 'openrouter:qwen/qwen-vl-plus', text: 'Qwen Qwen vl plus (OpenRouter)' },
        { value: 'openrouter:qwen/qwen-vl-max', text: 'Qwen Qwen vl max (OpenRouter)' },
        { value: 'openrouter:qwen/qwen-turbo', text: 'Qwen Qwen turbo (OpenRouter)' },
        { value: 'openrouter:qwen/qwen2.5-vl-72b-instruct:free', text: 'Qwen Qwen2.5 vl 72b instruct:free (OpenRouter)' },
        { value: 'openrouter:qwen/qwen2.5-vl-72b-instruct', text: 'Qwen Qwen2.5 vl 72b instruct (OpenRouter)' },
        { value: 'openrouter:qwen/qwen-plus', text: 'Qwen Qwen plus (OpenRouter)' },
        { value: 'openrouter:qwen/qwen-max', text: 'Qwen Qwen max (OpenRouter)' },
        { value: 'openrouter:qwen/qwq-32b-preview', text: 'Qwen qwq 32b preview (OpenRouter)' },
        { value: 'openrouter:qwen/qwen-2.5-coder-32b-instruct:free', text: 'Qwen Qwen 2.5 coder 32b instruct:free (OpenRouter)' },
        { value: 'openrouter:qwen/qwen-2.5-coder-32b-instruct', text: 'Qwen Qwen 2.5 coder 32b instruct (OpenRouter)' },
        { value: 'openrouter:qwen/qwen-2.5-7b-instruct', text: 'Qwen Qwen 2.5 7b instruct (OpenRouter)' },
        { value: 'openrouter:qwen/qwen-2.5-72b-instruct:free', text: 'Qwen Qwen 2.5 72b instruct:free (OpenRouter)' },
        { value: 'openrouter:qwen/qwen-2.5-72b-instruct', text: 'Qwen Qwen 2.5 72b instruct (OpenRouter)' },
        { value: 'openrouter:qwen/qwen-2.5-vl-7b-instruct', text: 'Qwen Qwen 2.5 vl 7b instruct (OpenRouter)' },
        { value: 'openrouter:qwen/qwen-2-72b-instruct', text: 'Qwen Qwen 2 72b instruct (OpenRouter)' }
    ],
    cohere: [
        { value: 'openrouter:cohere/command-a', text: 'cohere command a (OpenRouter)' },
        { value: 'openrouter:cohere/command-r7b-12-2024', text: 'cohere command r7b 12 2024 (OpenRouter)' },
        { value: 'openrouter:cohere/command-r-plus-08-2024', text: 'cohere command r plus 08 2024 (OpenRouter)' },
        { value: 'openrouter:cohere/command-r-08-2024', text: 'cohere command r 08 2024 (OpenRouter)' },
        { value: 'openrouter:cohere/command-r-plus', text: 'cohere command r plus (OpenRouter)' },
        { value: 'openrouter:cohere/command-r-plus-04-2024', text: 'cohere command r plus 04 2024 (OpenRouter)' },
        { value: 'openrouter:cohere/command', text: 'cohere command (OpenRouter)' },
        { value: 'openrouter:cohere/command-r', text: 'cohere command r (OpenRouter)' },
        { value: 'openrouter:cohere/command-r-03-2024', text: 'cohere command r 03 2024 (OpenRouter)' }
    ],
    amazon: [
        { value: 'openrouter:amazon/nova-lite-v1', text: 'amazon nova lite v1 (OpenRouter)' },
        { value: 'openrouter:amazon/nova-micro-v1', text: 'amazon nova micro v1 (OpenRouter)' },
        { value: 'openrouter:amazon/nova-pro-v1', text: 'amazon nova pro v1 (OpenRouter)' }
    ],
    xai: [
        { value: 'grok-beta', text: 'Grok beta' },
        { value: 'grok-vision-beta', text: 'Grok vision beta' },
        { value: 'grok-3', text: 'Grok 3' },
        { value: 'grok-3-fast', text: 'Grok 3 fast' },
        { value: 'grok-3-mini', text: 'Grok 3 mini' },
        { value: 'grok-3-mini-fast', text: 'Grok 3 mini fast' },
        { value: 'grok-2-vision', text: 'Grok 2 vision' },
        { value: 'grok-2', text: 'Grok 2' },
        { value: 'openrouter:x-ai/grok-4', text: 'x ai Grok 4 (OpenRouter)' },
        { value: 'openrouter:x-ai/grok-3-mini', text: 'x ai Grok 3 mini (OpenRouter)' },
        { value: 'openrouter:x-ai/grok-3', text: 'x ai Grok 3 (OpenRouter)' },
        { value: 'openrouter:x-ai/grok-3-mini-beta', text: 'x ai Grok 3 mini beta (OpenRouter)' },
        { value: 'openrouter:x-ai/grok-3-beta', text: 'x ai Grok 3 beta (OpenRouter)' },
        { value: 'openrouter:x-ai/grok-2-vision-1212', text: 'x ai Grok 2 vision 1212 (OpenRouter)' },
        { value: 'openrouter:x-ai/grok-2-1212', text: 'x ai Grok 2 1212 (OpenRouter)' },
        { value: 'openrouter:x-ai/grok-vision-beta', text: 'x ai Grok vision beta (OpenRouter)' }
    ],
    microsoft: [
        { value: 'openrouter:microsoft/phi-4-reasoning-plus', text: 'microsoft phi 4 reasoning plus (OpenRouter)' },
        { value: 'openrouter:microsoft/mai-ds-r1:free', text: 'microsoft mai ds r1:free (OpenRouter)' },
        { value: 'openrouter:microsoft/phi-4-multimodal-instruct', text: 'microsoft phi 4 multimodal instruct (OpenRouter)' },
        { value: 'openrouter:microsoft/phi-4', text: 'microsoft phi 4 (OpenRouter)' },
        { value: 'openrouter:microsoft/phi-3.5-mini-128k-instruct', text: 'microsoft phi 3.5 mini 128k instruct (OpenRouter)' },
        { value: 'openrouter:microsoft/phi-3-mini-128k-instruct', text: 'microsoft phi 3 mini 128k instruct (OpenRouter)' },
        { value: 'openrouter:microsoft/phi-3-medium-128k-instruct', text: 'microsoft phi 3 medium 128k instruct (OpenRouter)' },
        { value: 'openrouter:microsoft/wizardlm-2-8x22b', text: 'microsoft wizardlm 2 8x22b (OpenRouter)' }
    ],
    nvidia: [
        { value: 'nvidia/Llama-3.1-Nemotron-70B-Instruct-HF', text: 'nvidia Llama 3.1 Nemotron 70B Instruct HF' },
        { value: 'openrouter:nvidia/llama-3.3-nemotron-super-49b-v1:free', text: 'nvidia Llama 3.3 nemotron super 49b v1:free (OpenRouter)' },
        { value: 'openrouter:nvidia/llama-3.3-nemotron-super-49b-v1', text: 'nvidia Llama 3.3 nemotron super 49b v1 (OpenRouter)' },
        { value: 'openrouter:nvidia/llama-3.1-nemotron-ultra-253b-v1:free', text: 'nvidia Llama 3.1 nemotron ultra 253b v1:free (OpenRouter)' },
        { value: 'openrouter:nvidia/llama-3.1-nemotron-ultra-253b-v1', text: 'nvidia Llama 3.1 nemotron ultra 253b v1 (OpenRouter)' },
        { value: 'openrouter:nvidia/llama-3.1-nemotron-70b-instruct', text: 'nvidia Llama 3.1 nemotron 70b instruct (OpenRouter)' }
    ],
    other: [
        { value: 'cartesia/sonic', text: 'cartesia sonic' },
        { value: 'black-forest-labs/FLUX.1-kontext-pro', text: 'black forest labs FLUX.1 kontext pro' },
        { value: 'Alibaba-NLP/gte-modernbert-base', text: 'Alibaba NLP gte modernbert base' },
        { value: 'marin-community/marin-8b-instruct', text: 'marin community marin 8b instruct' },
        { value: 'arcee_ai/arcee-spotlight', text: 'arcee ai arcee spotlight' },
        { value: 'black-forest-labs/FLUX.1-canny', text: 'black forest labs FLUX.1 canny' },
        { value: 'cartesia/sonic-2', text: 'cartesia sonic 2' },
        { value: 'lgai/exaone-3-5-32b-instruct', text: 'lgai exaone 3 5 32b instruct' },
        { value: 'togethercomputer/MoA-1', text: 'togethercomputer MoA 1' },
        { value: 'togethercomputer/m2-bert-80M-32k-retrieval', text: 'togethercomputer m2 bert 80M 32k retrieval' },
        { value: 'black-forest-labs/FLUX.1-kontext-dev', text: 'black forest labs FLUX.1 kontext dev' },
        { value: 'black-forest-labs/FLUX.1-dev', text: 'black forest labs FLUX.1 dev' },
        { value: 'black-forest-labs/FLUX.1-dev-lora', text: 'black forest labs FLUX.1 dev lora' },
        { value: 'arcee-ai/AFM-4.5B-Preview', text: 'arcee ai AFM 4.5B Preview' },
        { value: 'togethercomputer/Refuel-Llm-V2', text: 'togethercomputer Refuel Llm V2' },
        { value: 'black-forest-labs/FLUX.1.1-pro', text: 'black forest labs FLUX.1.1 pro' },
        { value: 'togethercomputer/MoA-1-Turbo', text: 'togethercomputer MoA 1 Turbo' },
        { value: 'mixedbread-ai/Mxbai-Rerank-Large-V2', text: 'mixedbread ai Mxbai Rerank Large V2' },
        { value: 'black-forest-labs/FLUX.1-depth', text: 'black forest labs FLUX.1 depth' },
        { value: 'black-forest-labs/FLUX.1-redux', text: 'black forest labs FLUX.1 redux' },
        { value: 'openai/whisper-large-v3', text: 'openai whisper large v3' },
        { value: 'arcee-ai/caller', text: 'arcee ai caller' },
        { value: 'eddiehou/meta-llama/Llama-3.1-405B', text: 'eddiehou meta Llama Llama 3.1 405B' },
        { value: 'perplexity-ai/r1-1776', text: 'perplexity ai r1 1776' },
        { value: 'NousResearch/Nous-Hermes-2-Mixtral-8x7B-DPO', text: 'NousResearch Nous Hermes 2 Mixtral 8x7B DPO' },
        { value: 'black-forest-labs/FLUX.1-pro', text: 'black forest labs FLUX.1 pro' },
        { value: 'serverless-qwen-qwen3-32b-fp8', text: 'serverless Qwen Qwen3 32b fp8' },
        { value: 'togethercomputer/Refuel-Llm-V2-Small', text: 'togethercomputer Refuel Llm V2 Small' },
        { value: 'scb10x/scb10x-llama3-1-typhoon2-70b-instruct', text: 'scb10x scb10x Llama3 1 typhoon2 70b instruct' },
        { value: 'arcee-ai/maestro-reasoning', text: 'arcee ai maestro reasoning' },
        { value: 'lgai/exaone-deep-32b', text: 'lgai exaone deep 32b' },
        { value: 'scb10x/scb10x-typhoon-2-1-gemma3-12b', text: 'scb10x scb10x typhoon 2 1 gemma3 12b' },
        { value: 'arcee-ai/virtuoso-medium-v2', text: 'arcee ai virtuoso medium v2' },
        { value: 'arcee-ai/coder-large', text: 'arcee ai coder large' },
        { value: 'arcee-ai/virtuoso-large', text: 'arcee ai virtuoso large' },
        { value: 'intfloat/multilingual-e5-large-instruct', text: 'intfloat multilingual e5 large instruct' },
        { value: 'black-forest-labs/FLUX.1-schnell', text: 'black forest labs FLUX.1 schnell' },
        { value: 'arcee-ai/arcee-blitz', text: 'arcee ai arcee blitz' },
        { value: 'Salesforce/Llama-Rank-V1', text: 'Salesforce Llama Rank V1' },
        { value: 'black-forest-labs/FLUX.1-schnell-Free', text: 'black forest labs FLUX.1 schnell Free' },
        { value: 'yan/deepseek-ai-deepseek-v3', text: 'yan DeepSeek ai DeepSeek v3' },
        { value: 'black-forest-labs/FLUX.1-kontext-max', text: 'black forest labs FLUX.1 kontext max' },
        { value: 'moz/Llama-3.3-70B-Instruct-Turbo', text: 'moz Llama 3.3 70B Instruct Turbo' },
        { value: 'model-fallback-test-1', text: 'model fallback test 1' },
        { value: 'openrouter:thudm/glm-4.1v-9b-thinking', text: 'thudm glm 4.1v 9b thinking (OpenRouter)' },
        { value: 'openrouter:cognitivecomputations/dolphin-mistral-24b-venice-edition:free', text: 'cognitivecomputations dolphin Mistral 24b venice edition:free (OpenRouter)' },
        { value: 'openrouter:tencent/hunyuan-a13b-instruct:free', text: 'tencent hunyuan a13b instruct:free (OpenRouter)' },
        { value: 'openrouter:tngtech/deepseek-r1t2-chimera:free', text: 'tngtech DeepSeek r1t2 chimera:free (OpenRouter)' },
        { value: 'openrouter:morph/morph-v3-large', text: 'morph morph v3 large (OpenRouter)' },
        { value: 'openrouter:morph/morph-v3-fast', text: 'morph morph v3 fast (OpenRouter)' },
        { value: 'openrouter:openrouter/cypher-alpha:free', text: 'openrouter cypher alpha:free (OpenRouter)' },
        { value: 'openrouter:baidu/ernie-4.5-300b-a47b', text: 'baidu ernie 4.5 300b a47b (OpenRouter)' },
        { value: 'openrouter:thedrummer/anubis-70b-v1.1', text: 'thedrummer anubis 70b v1.1 (OpenRouter)' },
        { value: 'openrouter:inception/mercury', text: 'inception mercury (OpenRouter)' },
        { value: 'openrouter:morph/morph-v2', text: 'morph morph v2 (OpenRouter)' },
        { value: 'openrouter:minimax/minimax-m1', text: 'minimax minimax m1 (OpenRouter)' },
        { value: 'openrouter:moonshotai/kimi-dev-72b:free', text: 'moonshotai kimi dev 72b:free (OpenRouter)' },
        { value: 'openrouter:sarvamai/sarvam-m:free', text: 'sarvamai sarvam m:free (OpenRouter)' },
        { value: 'openrouter:thedrummer/valkyrie-49b-v1', text: 'thedrummer valkyrie 49b v1 (OpenRouter)' },
        { value: 'openrouter:arcee-ai/caller-large', text: 'arcee ai caller large (OpenRouter)' },
        { value: 'openrouter:arcee-ai/spotlight', text: 'arcee ai spotlight (OpenRouter)' },
        { value: 'openrouter:arcee-ai/maestro-reasoning', text: 'arcee ai maestro reasoning (OpenRouter)' },
        { value: 'openrouter:arcee-ai/virtuoso-large', text: 'arcee ai virtuoso large (OpenRouter)' },
        { value: 'openrouter:arcee-ai/coder-large', text: 'arcee ai coder large (OpenRouter)' },
        { value: 'openrouter:arcee-ai/virtuoso-medium-v2', text: 'arcee ai virtuoso medium v2 (OpenRouter)' },
        { value: 'openrouter:arcee-ai/arcee-blitz', text: 'arcee ai arcee blitz (OpenRouter)' },
        { value: 'openrouter:inception/mercury-coder', text: 'inception mercury coder (OpenRouter)' },
        { value: 'openrouter:opengvlab/internvl3-14b', text: 'opengvlab internvl3 14b (OpenRouter)' },
        { value: 'openrouter:tngtech/deepseek-r1t-chimera:free', text: 'tngtech DeepSeek r1t chimera:free (OpenRouter)' },
        { value: 'openrouter:thudm/glm-z1-32b:free', text: 'thudm glm z1 32b:free (OpenRouter)' },
        { value: 'openrouter:thudm/glm-4-32b:free', text: 'thudm glm 4 32b:free (OpenRouter)' },
        { value: 'openrouter:thudm/glm-4-32b', text: 'thudm glm 4 32b (OpenRouter)' },
        { value: 'openrouter:shisa-ai/shisa-v2-llama3.3-70b:free', text: 'shisa ai shisa v2 Llama3.3 70b:free (OpenRouter)' },
        { value: 'openrouter:eleutherai/llemma_7b', text: 'eleutherai llemma 7b (OpenRouter)' },
        { value: 'openrouter:alfredpros/codellama-7b-instruct-solidity', text: 'alfredpros codeLlama 7b instruct solidity (OpenRouter)' },
        { value: 'openrouter:arliai/qwq-32b-arliai-rpr-v1:free', text: 'arliai qwq 32b arliai rpr v1:free (OpenRouter)' },
        { value: 'openrouter:agentica-org/deepcoder-14b-preview:free', text: 'agentica org deepcoder 14b preview:free (OpenRouter)' },
        { value: 'openrouter:moonshotai/kimi-vl-a3b-thinking:free', text: 'moonshotai kimi vl a3b thinking:free (OpenRouter)' },
        { value: 'openrouter:scb10x/llama3.1-typhoon2-70b-instruct', text: 'scb10x Llama3.1 typhoon2 70b instruct (OpenRouter)' },
        { value: 'openrouter:featherless/qwerky-72b:free', text: 'featherless qwerky 72b:free (OpenRouter)' },
        { value: 'openrouter:ai21/jamba-1.6-large', text: 'ai21 jamba 1.6 large (OpenRouter)' },
        { value: 'openrouter:ai21/jamba-1.6-mini', text: 'ai21 jamba 1.6 mini (OpenRouter)' },
        { value: 'openrouter:rekaai/reka-flash-3:free', text: 'rekaai reka flash 3:free (OpenRouter)' },
        { value: 'openrouter:thedrummer/anubis-pro-105b-v1', text: 'thedrummer anubis pro 105b v1 (OpenRouter)' },
        { value: 'openrouter:thedrummer/skyfall-36b-v2', text: 'thedrummer skyfall 36b v2 (OpenRouter)' },
        { value: 'openrouter:perplexity/sonar-reasoning-pro', text: 'perplexity sonar reasoning pro (OpenRouter)' },
        { value: 'openrouter:perplexity/sonar-pro', text: 'perplexity sonar pro (OpenRouter)' },
        { value: 'openrouter:perplexity/sonar-deep-research', text: 'perplexity sonar deep research (OpenRouter)' },
        { value: 'openrouter:nousresearch/deephermes-3-llama-3-8b-preview:free', text: 'nousresearch deephermes 3 Llama 3 8b preview:free (OpenRouter)' },
        { value: 'openrouter:perplexity/r1-1776', text: 'perplexity r1 1776 (OpenRouter)' },
        { value: 'openrouter:cognitivecomputations/dolphin3.0-r1-mistral-24b:free', text: 'cognitivecomputations dolphin3.0 r1 Mistral 24b:free (OpenRouter)' },
        { value: 'openrouter:cognitivecomputations/dolphin3.0-mistral-24b:free', text: 'cognitivecomputations dolphin3.0 Mistral 24b:free (OpenRouter)' },
        { value: 'openrouter:aion-labs/aion-1.0', text: 'aion labs aion 1.0 (OpenRouter)' },
        { value: 'openrouter:aion-labs/aion-1.0-mini', text: 'aion labs aion 1.0 mini (OpenRouter)' },
        { value: 'openrouter:aion-labs/aion-rp-llama-3.1-8b', text: 'aion labs aion rp Llama 3.1 8b (OpenRouter)' },
        { value: 'openrouter:perplexity/sonar-reasoning', text: 'perplexity sonar reasoning (OpenRouter)' },
        { value: 'openrouter:perplexity/sonar', text: 'perplexity sonar (OpenRouter)' },
        { value: 'openrouter:liquid/lfm-7b', text: 'liquid lfm 7b (OpenRouter)' },
        { value: 'openrouter:liquid/lfm-3b', text: 'liquid lfm 3b (OpenRouter)' },
        { value: 'openrouter:minimax/minimax-01', text: 'minimax minimax 01 (OpenRouter)' },
        { value: 'openrouter:sao10k/l3.3-euryale-70b', text: 'sao10k l3.3 euryale 70b (OpenRouter)' },
        { value: 'openrouter:eva-unit-01/eva-llama-3.33-70b', text: 'eva unit 01 eva Llama 3.33 70b (OpenRouter)' },
        { value: 'openrouter:eva-unit-01/eva-qwen-2.5-72b', text: 'eva unit 01 eva Qwen 2.5 72b (OpenRouter)' },
        { value: 'openrouter:infermatic/mn-inferor-12b', text: 'infermatic mn inferor 12b (OpenRouter)' },
        { value: 'openrouter:raifle/sorcererlm-8x22b', text: 'raifle sorcererlm 8x22b (OpenRouter)' },
        { value: 'openrouter:thedrummer/unslopnemo-12b', text: 'thedrummer unslopnemo 12b (OpenRouter)' },
        { value: 'openrouter:anthracite-org/magnum-v4-72b', text: 'anthracite org magnum v4 72b (OpenRouter)' },
        { value: 'openrouter:inflection/inflection-3-productivity', text: 'inflection inflection 3 productivity (OpenRouter)' },
        { value: 'openrouter:inflection/inflection-3-pi', text: 'inflection inflection 3 pi (OpenRouter)' },
        { value: 'openrouter:thedrummer/rocinante-12b', text: 'thedrummer rocinante 12b (OpenRouter)' },
        { value: 'openrouter:anthracite-org/magnum-v2-72b', text: 'anthracite org magnum v2 72b (OpenRouter)' },
        { value: 'openrouter:liquid/lfm-40b', text: 'liquid lfm 40b (OpenRouter)' },
        { value: 'openrouter:neversleep/llama-3.1-lumimaid-8b', text: 'neversleep Llama 3.1 lumimaid 8b (OpenRouter)' },
        { value: 'openrouter:sao10k/l3.1-euryale-70b', text: 'sao10k l3.1 euryale 70b (OpenRouter)' },
        { value: 'openrouter:nousresearch/hermes-3-llama-3.1-70b', text: 'nousresearch hermes 3 Llama 3.1 70b (OpenRouter)' },
        { value: 'openrouter:nousresearch/hermes-3-llama-3.1-405b', text: 'nousresearch hermes 3 Llama 3.1 405b (OpenRouter)' },
        { value: 'openrouter:sao10k/l3-lunaris-8b', text: 'sao10k l3 lunaris 8b (OpenRouter)' },
        { value: 'openrouter:aetherwiing/mn-starcannon-12b', text: 'aetherwiing mn starcannon 12b (OpenRouter)' },
        { value: 'openrouter:nothingiisreal/mn-celeste-12b', text: 'nothingiisreal mn celeste 12b (OpenRouter)' },
        { value: 'openrouter:alpindale/magnum-72b', text: 'alpindale magnum 72b (OpenRouter)' },
        { value: 'openrouter:01-ai/yi-large', text: '01 ai yi large (OpenRouter)' },
        { value: 'openrouter:sao10k/l3-euryale-70b', text: 'sao10k l3 euryale 70b (OpenRouter)' },
        { value: 'openrouter:cognitivecomputations/dolphin-mixtral-8x22b', text: 'cognitivecomputations dolphin mixtral 8x22b (OpenRouter)' },
        { value: 'openrouter:nousresearch/hermes-2-pro-llama-3-8b', text: 'nousresearch hermes 2 pro Llama 3 8b (OpenRouter)' },
        { value: 'openrouter:neversleep/llama-3-lumimaid-70b', text: 'neversleep Llama 3 lumimaid 70b (OpenRouter)' },
        { value: 'openrouter:sao10k/fimbulvetr-11b-v2', text: 'sao10k fimbulvetr 11b v2 (OpenRouter)' },
        { value: 'openrouter:sophosympatheia/midnight-rose-70b', text: 'sophosympatheia midnight rose 70b (OpenRouter)' },
        { value: 'openrouter:nousresearch/nous-hermes-2-mixtral-8x7b-dpo', text: 'nousresearch nous hermes 2 mixtral 8x7b dpo (OpenRouter)' },
        { value: 'openrouter:neversleep/noromaid-20b', text: 'neversleep noromaid 20b (OpenRouter)' },
        { value: 'openrouter:undi95/toppy-m-7b', text: 'undi95 toppy m 7b (OpenRouter)' },
        { value: 'openrouter:alpindale/goliath-120b', text: 'alpindale goliath 120b (OpenRouter)' },
        { value: 'openrouter:openrouter/auto', text: 'openrouter auto (OpenRouter)' },
        { value: 'openrouter:pygmalionai/mythalion-13b', text: 'pygmalionai mythalion 13b (OpenRouter)' },
        { value: 'openrouter:mancer/weaver', text: 'mancer weaver (OpenRouter)' },
        { value: 'openrouter:undi95/remm-slerp-l2-13b', text: 'undi95 remm slerp l2 13b (OpenRouter)' },
        { value: 'openrouter:gryphe/mythomax-l2-13b', text: 'gryphe mythomax l2 13b (OpenRouter)' }
    ],
};;
            }
            
            initializeElements() {
                this.providerSelect = document.getElementById('providerSelect');
                this.modelSelect = document.getElementById('modelSelect');
                this.customModelInput = document.getElementById('customModelInput');
                this.customModel = document.getElementById('customModel');
                this.chatMessages = document.getElementById('chatMessages');
                this.messageInput = document.getElementById('messageInput');
                this.sendBtn = document.getElementById('sendBtn');
                this.attachBtn = document.getElementById('attachBtn');
                this.fileInput = document.getElementById('fileInput');
                this.fileUploadArea = document.getElementById('fileUploadArea');
                this.filePreview = document.getElementById('filePreview');
                this.filePreviewList = document.getElementById('filePreviewList');
                this.saveHistoryBtn = document.getElementById('saveHistory');
                this.loadHistoryBtn = document.getElementById('loadHistory');
                this.clearHistoryBtn = document.getElementById('clearHistory');
                this.historyFileInput = document.getElementById('historyFileInput');
                this.messageCountSpan = document.getElementById('messageCount');
                this.currentModelSpan = document.getElementById('currentModel');
            }
            
            setupEventListeners() {
                // 发送消息
                this.sendBtn.addEventListener('click', () => this.sendMessage());
                this.messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        this.sendMessage();
                    }
                });
                
                // 厂商选择
                this.providerSelect.addEventListener('change', () => {
                    this.updateModelOptions();
                });
                
                // 模型选择
                this.modelSelect.addEventListener('change', () => {
                    this.updateCurrentModel();
                });
                
                // 自定义模型输入
                this.customModel.addEventListener('input', () => {
                    this.updateCurrentModel();
                });
                
                this.customModel.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        this.updateCurrentModel();
                    }
                });
                
                // 文件上传
                this.attachBtn.addEventListener('click', () => {
                    this.fileUploadArea.classList.toggle('hidden');
                    if (!this.fileUploadArea.classList.contains('hidden')) {
                        this.fileInput.click();
                    }
                });
                
                this.fileInput.addEventListener('change', (e) => this.handleFileSelect(e));
                
                // 拖拽上传
                this.fileUploadArea.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    this.fileUploadArea.classList.add('drag-over');
                });
                
                this.fileUploadArea.addEventListener('dragleave', () => {
                    this.fileUploadArea.classList.remove('drag-over');
                });
                
                this.fileUploadArea.addEventListener('drop', (e) => {
                    e.preventDefault();
                    this.fileUploadArea.classList.remove('drag-over');
                    this.handleFileSelect(e);
                });
                
                this.fileUploadArea.addEventListener('click', () => {
                    this.fileInput.click();
                });
                
                // 历史记录管理
                this.saveHistoryBtn.addEventListener('click', () => this.saveHistory());
                this.loadHistoryBtn.addEventListener('click', () => this.loadHistory());
                this.clearHistoryBtn.addEventListener('click', () => this.clearHistory());
                this.historyFileInput.addEventListener('change', (e) => this.handleHistoryFileLoad(e));
            }
            
            updateModelOptions() {
                const selectedProvider = this.providerSelect.value;
                
                // 清空模型选择
                this.modelSelect.innerHTML = '';
                
                if (!selectedProvider) {
                    this.modelSelect.innerHTML = '<option value="">请先选择厂商</option>';
                    this.modelSelect.disabled = true;
                    this.customModelInput.classList.add('hidden');
                    return;
                }
                
                // 检查是否选择了自定义模型
                if (selectedProvider === 'custom') {
                    this.modelSelect.innerHTML = '<option value="">使用下方自定义输入</option>';
                    this.modelSelect.disabled = true;
                    this.customModelInput.classList.remove('hidden');
                } else {
                    // 启用模型选择，隐藏自定义输入
                    this.modelSelect.disabled = false;
                    this.customModelInput.classList.add('hidden');
                    
                    // 添加默认选项
                    this.modelSelect.innerHTML = '<option value="">请选择模型</option>';
                    
                    // 添加对应厂商的模型
                    const models = this.modelData[selectedProvider];
                    if (models) {
                        models.forEach(model => {
                            const option = document.createElement('option');
                            option.value = model.value;
                            option.textContent = model.text;
                            this.modelSelect.appendChild(option);
                        });
                    }
                }
                
                // 更新当前模型显示
                this.updateCurrentModel();
            }
            
            handleFileSelect(e) {
                const files = e.target.files || e.dataTransfer.files;
                for (let file of files) {
                    this.currentFiles.push(file);
                    this.addFilePreview(file);
                }
                this.fileUploadArea.classList.add('hidden');
            }
            
            addFilePreview(file) {
                this.filePreview.classList.remove('hidden');
                
                const fileElement = document.createElement('div');
                fileElement.className = 'bg-blue-50 p-3 rounded-lg border border-blue-200 flex items-center space-x-2';
                
                const fileIcon = this.getFileIcon(file.type);
                const fileName = file.name.length > 20 ? file.name.substring(0, 20) + '...' : file.name;
                
                fileElement.innerHTML = `
                    <i class="fas ${fileIcon} text-blue-600"></i>
                    <span class="text-sm text-blue-700">${fileName}</span>
                    <button class="text-red-500 hover:text-red-700 ml-auto" onclick="this.parentElement.remove()">
                        <i class="fas fa-times"></i>
                    </button>
                `;
                
                this.filePreviewList.appendChild(fileElement);
            }
            
            getFileIcon(fileType) {
                if (fileType.startsWith('image/')) return 'fa-image';
                if (fileType.startsWith('video/')) return 'fa-video';
                if (fileType.startsWith('audio/')) return 'fa-music';
                if (fileType.includes('pdf')) return 'fa-file-pdf';
                if (fileType.includes('word')) return 'fa-file-word';
                if (fileType.includes('text')) return 'fa-file-text';
                return 'fa-file';
            }
            
            async sendMessage() {
                if (this.isProcessing) return;
                
                const message = this.messageInput.value.trim();
                if (!message && this.currentFiles.length === 0) return;
                
                // 获取当前模型
                let currentModel;
                const selectedProvider = this.providerSelect.value;
                
                if (selectedProvider === 'custom') {
                    currentModel = this.customModel.value.trim();
                    if (!currentModel) {
                        alert('请输入自定义模型名称');
                        return;
                    }
                } else {
                    currentModel = this.modelSelect.value;
                    if (!currentModel) {
                        alert('请先选择厂商和模型');
                        return;
                    }
                }
                
                this.isProcessing = true;
                this.sendBtn.disabled = true;
                
                // 添加用户消息
                this.addMessage('user', message, this.currentFiles);
                
                // 清空输入
                this.messageInput.value = '';
                this.currentFiles = [];
                this.filePreview.classList.add('hidden');
                this.filePreviewList.innerHTML = '';
                
                // 添加AI思考指示器
                const thinkingDiv = this.addThinkingIndicator();
                
                try {
                    // 准备消息历史
                    const messageHistory = this.messages.map(msg => ({
                        role: msg.role,
                        content: msg.content
                    }));
                    
                    // 调用AI API
                    const response = await puter.ai.chat(
                        messageHistory,
                        {
                            model: currentModel,
                            stream: true
                        }
                    );
                    
                    // 移除思考指示器
                    thinkingDiv.remove();
                    
                    // 创建AI回复消息
                    const aiMessageDiv = this.createMessageElement('assistant', '');
                    this.chatMessages.appendChild(aiMessageDiv);
                    
                    const contentDiv = aiMessageDiv.querySelector('.message-content');
                    let fullResponse = '';
                    
                    // 处理流式响应
                    for await (const part of response) {
                        if (part?.text) {
                            fullResponse += part.text;
                            contentDiv.innerHTML = this.formatMessage(fullResponse);
                            this.scrollToBottom();
                        }
                    }
                    
                    // 保存完整的AI回复
                    this.messages.push({
                        role: 'assistant',
                        content: fullResponse,
                        timestamp: new Date()
                    });
                    
                } catch (error) {
                    console.error('Error:', error);
                    thinkingDiv.remove();
                    this.addMessage('assistant', '抱歉，发生了错误。请检查网络连接或稍后重试。');
                }
                
                this.isProcessing = false;
                this.sendBtn.disabled = false;
                this.updateStats();
                this.saveToLocalStorage();
            }
            
            addMessage(role, content, files = []) {
                const message = {
                    role: role,
                    content: content,
                    files: files,
                    timestamp: new Date()
                };
                
                this.messages.push(message);
                
                const messageDiv = this.createMessageElement(role, content, files);
                this.chatMessages.appendChild(messageDiv);
                this.scrollToBottom();
            }
            
            createMessageElement(role, content, files = []) {
                const messageDiv = document.createElement('div');
                messageDiv.className = `message flex ${role === 'user' ? 'justify-end' : 'justify-start'}`;
                
                const isUser = role === 'user';
                const bgColor = isUser ? 'bg-blue-600 text-white' : 'bg-gray-100 text-gray-800';
                const alignClass = isUser ? 'ml-12' : 'mr-12';
                
                let filesHtml = '';
                if (files && files.length > 0) {
                    filesHtml = `
                        <div class="mb-2">
                            ${files.map(file => `
                                <div class="inline-block bg-white bg-opacity-20 rounded px-2 py-1 text-xs mr-2 mb-1">
                                    <i class="fas ${this.getFileIcon(file.type)} mr-1"></i>
                                    ${file.name}
                                </div>
                            `).join('')}
                        </div>
                    `;
                }
                
                messageDiv.innerHTML = `
                    <div class="max-w-3xl ${alignClass}">
                        <div class="${bgColor} rounded-lg p-4">
                            <div class="flex items-center mb-2">
                                <i class="fas ${isUser ? 'fa-user' : 'fa-robot'} mr-2"></i>
                                <span class="font-medium">${isUser ? '用户' : 'AI助手'}</span>
                                <span class="text-xs opacity-75 ml-auto">${new Date().toLocaleTimeString()}</span>
                            </div>
                            ${filesHtml}
                            <div class="message-content">${this.formatMessage(content)}</div>
                        </div>
                    </div>
                `;
                
                return messageDiv;
            }
            
            addThinkingIndicator() {
                const thinkingDiv = document.createElement('div');
                thinkingDiv.className = 'message flex justify-start';
                thinkingDiv.innerHTML = `
                    <div class="max-w-3xl mr-12">
                        <div class="bg-gray-100 text-gray-800 rounded-lg p-4">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-robot mr-2"></i>
                                <span class="font-medium">AI助手</span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <span class="text-sm text-gray-600">正在思考</span>
                                <div class="typing-indicator"></div>
                                <div class="typing-indicator"></div>
                                <div class="typing-indicator"></div>
                            </div>
                        </div>
                    </div>
                `;
                
                this.chatMessages.appendChild(thinkingDiv);
                this.scrollToBottom();
                return thinkingDiv;
            }
            
            formatMessage(content) {
                // 首先转义HTML标签，防止被渲染
                let escapedContent = content
                    .replace(/&/g, '&amp;')
                    .replace(/</g, '&lt;')
                    .replace(/>/g, '&gt;')
                    .replace(/"/g, '&quot;')
                    .replace(/'/g, '&#39;');
                
                // 处理代码块（```语法）
                escapedContent = escapedContent.replace(/```(\w+)?\n([\s\S]*?)```/g, function(match, language, code) {
                    const lang = language || 'text';
                    return `<div class="bg-gray-900 text-gray-100 p-4 rounded-lg my-2 overflow-x-auto">
                        <div class="text-xs text-gray-400 mb-2">${lang}</div>
                        <pre><code>${code.trim()}</code></pre>
                    </div>`;
                });
                
                // 处理行内代码
                escapedContent = escapedContent.replace(/`([^`]+)`/g, '<code class="bg-gray-200 px-1 rounded text-sm">$1</code>');
                
                // 基本的markdown格式化
                escapedContent = escapedContent
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/\*(.*?)\*/g, '<em>$1</em>')
                    .replace(/\n/g, '<br>');
                
                return escapedContent;
            }
            
            scrollToBottom() {
                this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
            }
            
            updateStats() {
                this.messageCountSpan.textContent = this.messages.length;
            }
            
            updateCurrentModel() {
                const selectedProvider = this.providerSelect.value;
                
                if (selectedProvider === 'custom') {
                    const customModel = this.customModel.value.trim();
                    if (customModel) {
                        this.currentModelSpan.textContent = customModel;
                    } else {
                        this.currentModelSpan.textContent = '自定义模型 (未输入)';
                    }
                } else {
                    const selectedModel = this.modelSelect.value;
                    if (selectedModel) {
                        const selectedOption = this.modelSelect.options[this.modelSelect.selectedIndex];
                        this.currentModelSpan.textContent = selectedOption.text;
                    } else {
                        this.currentModelSpan.textContent = '未选择';
                    }
                }
            }
            
            saveHistory() {
                const historyData = {
                    messages: this.messages,
                    model: this.modelSelect.value,
                    provider: this.providerSelect.value,
                    customModel: this.customModel.value.trim(),
                    timestamp: new Date().toISOString()
                };
                
                const dataStr = JSON.stringify(historyData, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});
                
                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `chat_history_${new Date().toISOString().split('T')[0]}.json`;
                link.click();
            }
            
            loadHistory() {
                this.historyFileInput.click();
            }
            
            handleHistoryFileLoad(e) {
                const file = e.target.files[0];
                if (!file) return;
                
                const reader = new FileReader();
                reader.onload = (event) => {
                    try {
                        const historyData = JSON.parse(event.target.result);
                        this.messages = historyData.messages || [];
                        
                        if (historyData.provider) {
                            this.providerSelect.value = historyData.provider;
                            this.updateModelOptions();
                        }
                        
                        // 处理自定义模型
                        if (historyData.provider === 'custom' && historyData.customModel) {
                            this.customModel.value = historyData.customModel;
                        }
                        
                        if (historyData.model) {
                            this.modelSelect.value = historyData.model;
                        }
                        
                        this.updateCurrentModel();
                        this.renderMessages();
                        this.updateStats();
                    } catch (error) {
                        alert('加载历史记录失败，请检查文件格式');
                    }
                };
                reader.readAsText(file);
            }
            
            clearHistory() {
                if (confirm('确定要清空所有对话历史吗？')) {
                    this.messages = [];
                    this.chatMessages.innerHTML = `
                        <div class="text-center text-gray-500 py-8">
                            <i class="fas fa-comments text-4xl mb-4"></i>
                            <p>开始与AI对话吧！支持文本、图片、文档等多种输入方式。</p>
                        </div>
                    `;
                    this.updateStats();
                    this.saveToLocalStorage();
                }
            }
            
            renderMessages() {
                this.chatMessages.innerHTML = '';
                
                if (this.messages.length === 0) {
                    this.chatMessages.innerHTML = `
                        <div class="text-center text-gray-500 py-8">
                            <i class="fas fa-comments text-4xl mb-4"></i>
                            <p>开始与AI对话吧！支持文本、图片、文档等多种输入方式。</p>
                        </div>
                    `;
                    return;
                }
                
                this.messages.forEach(message => {
                    const messageDiv = this.createMessageElement(message.role, message.content, message.files);
                    this.chatMessages.appendChild(messageDiv);
                });
                
                this.scrollToBottom();
            }
            
            saveToLocalStorage() {
                try {
                    localStorage.setItem('chatHistory', JSON.stringify(this.messages));
                    localStorage.setItem('selectedModel', this.modelSelect.value);
                    localStorage.setItem('selectedProvider', this.providerSelect.value);
                } catch (error) {
                    console.warn('无法保存到本地存储');
                }
            }
            
            loadFromLocalStorage() {
                try {
                    const savedHistory = localStorage.getItem('chatHistory');
                    const savedModel = localStorage.getItem('selectedModel');
                    const savedProvider = localStorage.getItem('selectedProvider');
                    
                    if (savedHistory) {
                        this.messages = JSON.parse(savedHistory);
                        this.renderMessages();
                        this.updateStats();
                    }
                    
                    // 设置默认厂商和模型
                    if (savedProvider) {
                        this.providerSelect.value = savedProvider;
                        this.updateModelOptions();
                    } else {
                        // 设置默认厂商为OpenAI
                        this.providerSelect.value = 'openai';
                        this.updateModelOptions();
                    }
                    
                    if (savedModel) {
                        this.modelSelect.value = savedModel;
                        this.updateCurrentModel();
                    } else {
                        // 设置默认模型为GPT-4.1
                        this.modelSelect.value = 'gpt-4.1';
                        this.updateCurrentModel();
                    }
                } catch (error) {
                    console.warn('无法从本地存储加载数据');
                }
            }
        }
        
        // 初始化应用
        document.addEventListener('DOMContentLoaded', () => {
            new ChatInterface();
        });
    </script>
</body>
</html>